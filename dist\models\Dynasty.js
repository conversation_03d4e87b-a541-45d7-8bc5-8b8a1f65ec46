"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const dynastySchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    name: {
        type: String,
        required: [true, 'Dynasty name is required'],
        minlength: [3, 'Dynasty name must be at least 3 characters'],
        maxlength: [20, 'Dynasty name cannot exceed 20 characters'],
        validate: {
            validator: function (v) {
                // Alphanumeric, spaces, hyphens, underscores only
                return /^[a-zA-Z0-9\s\-_]+$/.test(v);
            },
            message: 'Dynasty name can only contain letters, numbers, spaces, hyphens, and underscores'
        }
    },
    founderId: {
        type: String,
        required: [true, 'Founder ID is required'],
        index: true
    },
    description: {
        type: String,
        maxlength: [200, 'Description cannot exceed 200 characters'],
        default: ''
    },
    level: {
        type: Number,
        default: 1,
        min: [1, 'Level must be at least 1'],
        max: [10, 'Level cannot exceed 10']
    },
    totalWealth: {
        type: Number,
        default: 0,
        min: [0, 'Total wealth cannot be negative']
    },
    memberCount: {
        type: Number,
        default: 1,
        min: [1, 'Member count must be at least 1']
    },
    activityScore: {
        type: Number,
        default: 0,
        min: [0, 'Activity score cannot be negative']
    },
    bankBalance: {
        type: Number,
        default: 0,
        min: [0, 'Bank balance cannot be negative']
    },
    taxRate: {
        type: Number,
        default: 0,
        min: [0, 'Tax rate cannot be negative'],
        max: [0.2, 'Tax rate cannot exceed 20%']
    },
    maxMembers: {
        type: Number,
        default: 20,
        min: [5, 'Dynasty must allow at least 5 members'],
        max: [50, 'Dynasty cannot exceed 50 members']
    },
    councilWithdrawalLimit: {
        type: Number,
        default: 100,
        min: [0, 'Withdrawal limit cannot be negative']
    },
    invitesPending: {
        type: Number,
        default: 0,
        min: [0, 'Pending invites cannot be negative'],
        max: [5, 'Cannot have more than 5 pending invites']
    },
    lastActivityAt: {
        type: Date,
        default: Date.now
    },
    settings: {
        publicProfile: {
            type: Boolean,
            default: true
        },
        autoAcceptInvites: {
            type: Boolean,
            default: false
        },
        requireApprovalForWithdrawals: {
            type: Boolean,
            default: false
        }
    }
}, {
    timestamps: true
});
// Compound indexes for efficient queries
dynastySchema.index({ guildId: 1, name: 1 }, { unique: true });
dynastySchema.index({ guildId: 1, level: -1, totalWealth: -1 });
dynastySchema.index({ founderId: 1 });
dynastySchema.index({ lastActivityAt: 1 });
exports.default = (0, mongoose_1.model)('Dynasty', dynastySchema);
