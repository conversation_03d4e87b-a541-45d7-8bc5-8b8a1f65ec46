"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EMOJIS = exports.COLORS = void 0;
exports.createBaseEmbed = createBaseEmbed;
exports.createSuccessEmbed = createSuccessEmbed;
exports.createErrorEmbed = createErrorEmbed;
exports.createEconomyEmbed = createEconomyEmbed;
exports.createAdminEmbed = createAdminEmbed;
exports.addUserInfo = addUserInfo;
exports.createQuickActionButtons = createQuickActionButtons;
exports.createNavigationButtons = createNavigationButtons;
exports.createConfirmationButtons = createConfirmationButtons;
exports.formatCoins = formatCoins;
exports.createLoadingEmbed = createLoadingEmbed;
const discord_js_1 = require("discord.js");
// Brand colors
exports.COLORS = {
    PRIMARY: '#dd7d00',
    SUCCESS: '#00ff00',
    ERROR: '#ff0000',
    WARNING: '#ffaa00',
    INFO: '#0099ff',
    GOLD: '#ffd700'
};
// Emoji collections
exports.EMOJIS = {
    ECONOMY: {
        COINS: '🪙',
        MONEY: '💰',
        DIAMOND: '💎',
        DOLLAR: '💲',
        SPARKLES: '✨',
        BANK: '🏦',
        CHART: '📈'
    },
    SUCCESS: {
        CHECK: '✅',
        PARTY: '🎉',
        STAR: '⭐',
        CROWN: '👑',
        TROPHY: '🏆',
        THUMBS_UP: '👍'
    },
    ROLES: {
        MEDAL: '🏅',
        MASK: '🎭',
        SHIELD: '🔰',
        ARMOR: '🛡️',
        BADGE: '📛',
        RIBBON: '🎀'
    },
    ACTIONS: {
        LIGHTNING: '⚡',
        ROCKET: '🚀',
        TARGET: '🎯',
        FIRE: '🔥',
        MAGIC: '🪄',
        GEAR: '⚙️'
    },
    ADMIN: {
        SCALES: '⚖️',
        HAMMER: '🔨',
        WARNING: '⚠️',
        TOOLS: '🛠️',
        LOCK: '🔒',
        KEY: '🔑',
        INFO: 'ℹ️',
        LIST: '📋',
        CLOCK: '🕐',
        SETTINGS: '⚙️'
    },
    MILESTONE: {
        TROPHY: '🏆',
        STAR: '⭐',
        MEDAL: '🏅',
        STREAK: '🔥',
        DIVERSITY: '🌈',
        VOICE: '🎤',
        MESSAGE: '💬',
        ANNIVERSARY: '🎂',
        PROGRESS: '📊',
        ACHIEVEMENT: '🎯'
    },
    MISC: {
        CLOCK: '🕐',
        CALENDAR: '📅',
        BOOK: '📖',
        SCROLL: '📜',
        MAGNIFYING: '🔍',
        BELL: '🔔',
        LIGHTBULB: '💡',
        ID: '🆔',
        USER: '👤',
        GUILD: '🏰',
        ROLE: '🎭',
        SPARKLES: '✨',
        LINK: '🔗',
        TAG: '🏷️',
        ATTACHMENT: '📎'
    }
};
/**
 * Creates a base embed with consistent branding
 */
function createBaseEmbed(title, description, color = exports.COLORS.PRIMARY) {
    const embed = new discord_js_1.EmbedBuilder()
        .setColor(color)
        .setTimestamp();
    if (title)
        embed.setTitle(title);
    if (description)
        embed.setDescription(description);
    return embed;
}
/**
 * Creates a success embed
 */
function createSuccessEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.SUCCESS.CHECK} ${title}`, description, exports.COLORS.SUCCESS);
}
/**
 * Creates an error embed
 */
function createErrorEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.ADMIN.WARNING} ${title}`, description, exports.COLORS.ERROR);
}
/**
 * Creates an economy-themed embed
 */
function createEconomyEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.ECONOMY.COINS} ${title}`, description, exports.COLORS.PRIMARY);
}
/**
 * Creates an admin-themed embed
 */
function createAdminEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.ADMIN.HAMMER} ${title}`, description, exports.COLORS.WARNING);
}
/**
 * Adds user information to an embed
 */
function addUserInfo(embed, user) {
    return embed
        .setAuthor({
        name: user.displayName || user.username,
        iconURL: user.displayAvatarURL()
    })
        .setThumbnail(user.displayAvatarURL({ size: 128 }));
}
/**
 * Creates quick action buttons
 */
function createQuickActionButtons() {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId('quick_balance')
        .setLabel('Balance')
        .setEmoji(exports.EMOJIS.ECONOMY.COINS)
        .setStyle(discord_js_1.ButtonStyle.Secondary), new discord_js_1.ButtonBuilder()
        .setCustomId('quick_leaderboard')
        .setLabel('Leaderboard')
        .setEmoji(exports.EMOJIS.SUCCESS.TROPHY)
        .setStyle(discord_js_1.ButtonStyle.Secondary), new discord_js_1.ButtonBuilder()
        .setCustomId('quick_roles')
        .setLabel('Roles')
        .setEmoji(exports.EMOJIS.ROLES.MEDAL)
        .setStyle(discord_js_1.ButtonStyle.Secondary));
}
/**
 * Creates navigation buttons for paginated content
 */
function createNavigationButtons(currentPage, totalPages, disabled = false) {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId('nav_first')
        .setLabel('First')
        .setEmoji('⏮️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === 1), new discord_js_1.ButtonBuilder()
        .setCustomId('nav_prev')
        .setLabel('Previous')
        .setEmoji('◀️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === 1), new discord_js_1.ButtonBuilder()
        .setCustomId('nav_next')
        .setLabel('Next')
        .setEmoji('▶️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === totalPages), new discord_js_1.ButtonBuilder()
        .setCustomId('nav_last')
        .setLabel('Last')
        .setEmoji('⏭️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === totalPages));
}
/**
 * Creates confirmation buttons
 */
function createConfirmationButtons(confirmId, cancelId) {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId(confirmId)
        .setLabel('Confirm')
        .setEmoji(exports.EMOJIS.SUCCESS.CHECK)
        .setStyle(discord_js_1.ButtonStyle.Success), new discord_js_1.ButtonBuilder()
        .setCustomId(cancelId)
        .setLabel('Cancel')
        .setEmoji('❌')
        .setStyle(discord_js_1.ButtonStyle.Danger));
}
/**
 * Formats a number with appropriate emoji and styling
 */
function formatCoins(amount) {
    return `${exports.EMOJIS.ECONOMY.COINS} **${amount.toLocaleString()}** Phalanx Loyalty Coins`;
}
/**
 * Creates a loading embed
 */
function createLoadingEmbed(message = 'Processing...') {
    return createBaseEmbed(`${exports.EMOJIS.MISC.CLOCK} ${message}`, 'Please wait while we process your request.', exports.COLORS.INFO);
}
