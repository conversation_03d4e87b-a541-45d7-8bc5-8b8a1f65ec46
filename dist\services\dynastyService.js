"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DYNASTY_MAX_PENDING_INVITES = exports.DYNASTY_INVITE_EXPIRY_HOURS = exports.DYNASTY_MIN_SERVER_DAYS = exports.DYNASTY_MIN_BALANCE = exports.DYNASTY_CREATION_COST = void 0;
exports.checkDynastyCreationEligibility = checkDynastyCreationEligibility;
exports.validateDynastyName = validateDynastyName;
exports.isDynastyNameAvailable = isDynastyNameAvailable;
exports.createDynasty = createDynasty;
exports.getDynastyById = getDynastyById;
exports.getDynastyWithMemberInfo = getDynastyWithMemberInfo;
exports.getDynastyByName = getDynastyByName;
exports.getUserDynastyMembership = getUserDynastyMembership;
exports.getDynastyMembers = getDynastyMembers;
exports.calculateDynastyLevel = calculateDynastyLevel;
exports.updateDynastyStats = updateDynastyStats;
exports.getDynastyLeaderboard = getDynastyLeaderboard;
exports.getAllDynasties = getAllDynasties;
exports.sendDynastyInvitation = sendDynastyInvitation;
exports.acceptDynastyInvitation = acceptDynastyInvitation;
exports.declineDynastyInvitation = declineDynastyInvitation;
exports.getUserPendingInvitations = getUserPendingInvitations;
const Dynasty_1 = __importDefault(require("../models/Dynasty"));
const DynastyMember_1 = __importDefault(require("../models/DynastyMember"));
const DynastyInvite_1 = __importDefault(require("../models/DynastyInvite"));
const User_1 = __importDefault(require("../models/User"));
const economyService_1 = require("./economyService");
const errorHandler_1 = require("../utils/errorHandler");
const mongoose_1 = __importDefault(require("mongoose"));
// Constants
exports.DYNASTY_CREATION_COST = 1000;
exports.DYNASTY_MIN_BALANCE = 5000;
exports.DYNASTY_MIN_SERVER_DAYS = 30;
exports.DYNASTY_INVITE_EXPIRY_HOURS = 24;
exports.DYNASTY_MAX_PENDING_INVITES = 5;
// Reserved dynasty names (case-insensitive)
const RESERVED_NAMES = [
    'admin', 'administrator', 'mod', 'moderator', 'staff', 'bot', 'system',
    'phalanx', 'order', 'dynasty', 'guild', 'clan', 'team', 'group',
    'everyone', 'here', 'null', 'undefined', 'test', 'debug'
];
// Profanity filter (basic implementation - can be expanded)
const PROFANITY_WORDS = [
    'damn', 'hell', 'crap', 'stupid', 'idiot', 'moron', 'dumb'
    // Add more as needed
];
/**
 * Check if a user is eligible to create a dynasty
 */
async function checkDynastyCreationEligibility(discordId, guildId, client) {
    try {
        // Check if user already has a dynasty
        const existingMembership = await DynastyMember_1.default.findOne({
            discordId,
            guildId
        });
        if (existingMembership) {
            return { eligible: false, reason: 'You are already a member of a dynasty' };
        }
        // Check user balance
        const user = await User_1.default.findOne({ discordId });
        if (!user || user.balance < exports.DYNASTY_MIN_BALANCE) {
            return {
                eligible: false,
                reason: `You need at least ${exports.DYNASTY_MIN_BALANCE} PLC to create a dynasty (current: ${user?.balance || 0} PLC)`
            };
        }
        // Check server tenure
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            return { eligible: false, reason: 'Guild not found' };
        }
        const member = await guild.members.fetch(discordId).catch(() => null);
        if (!member) {
            return { eligible: false, reason: 'You are not a member of this server' };
        }
        const joinDate = member.joinedAt;
        if (!joinDate) {
            return { eligible: false, reason: 'Unable to determine your join date' };
        }
        const daysSinceJoin = Math.floor((Date.now() - joinDate.getTime()) / (1000 * 60 * 60 * 24));
        if (daysSinceJoin < exports.DYNASTY_MIN_SERVER_DAYS) {
            return {
                eligible: false,
                reason: `You must be a server member for at least ${exports.DYNASTY_MIN_SERVER_DAYS} days (current: ${daysSinceJoin} days)`
            };
        }
        return { eligible: true };
    }
    catch (error) {
        console.error('Error checking dynasty creation eligibility:', error);
        throw new errorHandler_1.DatabaseError('Failed to check dynasty creation eligibility');
    }
}
/**
 * Validate dynasty name
 */
function validateDynastyName(name) {
    // Trim and check length
    const trimmedName = name.trim();
    if (trimmedName.length < 3) {
        return { valid: false, reason: 'Dynasty name must be at least 3 characters long' };
    }
    if (trimmedName.length > 20) {
        return { valid: false, reason: 'Dynasty name cannot exceed 20 characters' };
    }
    // Check allowed characters
    if (!/^[a-zA-Z0-9\s\-_]+$/.test(trimmedName)) {
        return { valid: false, reason: 'Dynasty name can only contain letters, numbers, spaces, hyphens, and underscores' };
    }
    // Check reserved names
    if (RESERVED_NAMES.includes(trimmedName.toLowerCase())) {
        return { valid: false, reason: 'This name is reserved and cannot be used' };
    }
    // Check profanity
    const lowerName = trimmedName.toLowerCase();
    for (const word of PROFANITY_WORDS) {
        if (lowerName.includes(word)) {
            return { valid: false, reason: 'Dynasty name contains inappropriate content' };
        }
    }
    return { valid: true };
}
/**
 * Check if dynasty name is available in guild
 */
async function isDynastyNameAvailable(name, guildId) {
    const existingDynasty = await Dynasty_1.default.findOne({
        guildId,
        name: { $regex: new RegExp(`^${name}$`, 'i') } // Case-insensitive check
    });
    return !existingDynasty;
}
/**
 * Create a new dynasty
 */
async function createDynasty(founderId, guildId, name, description = '', client) {
    const session = await mongoose_1.default.startSession();
    try {
        return await session.withTransaction(async () => {
            // Validate eligibility
            const eligibility = await checkDynastyCreationEligibility(founderId, guildId, client);
            if (!eligibility.eligible) {
                throw new errorHandler_1.DatabaseError(eligibility.reason || 'Not eligible to create dynasty');
            }
            // Validate name
            const nameValidation = validateDynastyName(name);
            if (!nameValidation.valid) {
                throw new errorHandler_1.DatabaseError(nameValidation.reason || 'Invalid dynasty name');
            }
            // Check name availability
            const nameAvailable = await isDynastyNameAvailable(name, guildId);
            if (!nameAvailable) {
                throw new errorHandler_1.DatabaseError('A dynasty with this name already exists');
            }
            // Deduct creation cost
            await (0, economyService_1.adjustBalance)(founderId, -exports.DYNASTY_CREATION_COST, 'dynasty_creation', `Dynasty creation fee for "${name}"`, client, guildId);
            // Create dynasty
            const dynasty = await Dynasty_1.default.create([{
                    guildId,
                    name: name.trim(),
                    founderId,
                    description: description.trim(),
                    level: 1,
                    totalWealth: 0,
                    memberCount: 1,
                    activityScore: 0,
                    bankBalance: 0,
                    taxRate: 0,
                    maxMembers: 20,
                    councilWithdrawalLimit: 100,
                    invitesPending: 0,
                    lastActivityAt: new Date(),
                    settings: {
                        publicProfile: true,
                        autoAcceptInvites: false,
                        requireApprovalForWithdrawals: false
                    }
                }], { session });
            // Create founder membership
            const member = await DynastyMember_1.default.create([{
                    dynastyId: dynasty[0]._id.toString(),
                    discordId: founderId,
                    guildId,
                    role: 'founder',
                    joinedAt: new Date(),
                    lastActiveAt: new Date(),
                    contributionScore: 0,
                    totalContributed: 0,
                    milestonesAchieved: 0,
                    permissions: {
                        canInvite: true,
                        canKick: true,
                        canWithdraw: true,
                        canManageBank: true
                    }
                }], { session });
            console.log(`[Dynasty Service] Created dynasty "${name}" by ${founderId} in guild ${guildId}`);
            return { dynasty: dynasty[0], member: member[0] };
        });
    }
    catch (error) {
        console.error('Error creating dynasty:', error);
        if (error instanceof errorHandler_1.DatabaseError) {
            throw error;
        }
        throw new errorHandler_1.DatabaseError('Failed to create dynasty');
    }
    finally {
        await session.endSession();
    }
}
/**
 * Get dynasty by ID
 */
async function getDynastyById(dynastyId) {
    return await Dynasty_1.default.findById(dynastyId);
}
/**
 * Get dynasty with member info by ID
 */
async function getDynastyWithMemberInfo(dynastyId) {
    const dynasty = await Dynasty_1.default.findById(dynastyId);
    if (!dynasty)
        return null;
    const members = await DynastyMember_1.default.find({ dynastyId }).sort({ role: 1, joinedAt: 1 });
    return { dynasty, members };
}
/**
 * Get dynasty by name in guild
 */
async function getDynastyByName(name, guildId) {
    return await Dynasty_1.default.findOne({
        guildId,
        name: { $regex: new RegExp(`^${name}$`, 'i') }
    });
}
/**
 * Get user's dynasty membership
 */
async function getUserDynastyMembership(discordId, guildId) {
    return await DynastyMember_1.default.findOne({ discordId, guildId });
}
/**
 * Get dynasty members
 */
async function getDynastyMembers(dynastyId) {
    return await DynastyMember_1.default.find({ dynastyId }).sort({ role: 1, joinedAt: 1 });
}
/**
 * Calculate dynasty level based on weighted formula
 */
function calculateDynastyLevel(totalWealth, activityScore, memberCount, ageInDays) {
    // Weighted formula: 40% wealth, 30% activity, 20% members, 10% age
    const wealthScore = Math.min(totalWealth / 10000, 100); // Cap at 100k PLC = 100 points
    const activityScoreNormalized = Math.min(activityScore / 100, 100); // Cap at 10k activity = 100 points
    const memberScore = Math.min(memberCount * 2, 100); // Cap at 50 members = 100 points
    const ageScore = Math.min(ageInDays / 10, 100); // Cap at 1000 days = 100 points
    const totalScore = (wealthScore * 0.4) + (activityScoreNormalized * 0.3) + (memberScore * 0.2) + (ageScore * 0.1);
    // Convert to level (1-10)
    return Math.min(Math.max(Math.floor(totalScore / 10) + 1, 1), 10);
}
/**
 * Update dynasty statistics
 */
async function updateDynastyStats(dynastyId) {
    try {
        const dynasty = await Dynasty_1.default.findById(dynastyId);
        if (!dynasty)
            return;
        const members = await DynastyMember_1.default.find({ dynastyId });
        // Calculate total wealth from all members
        let totalWealth = dynasty.bankBalance;
        for (const member of members) {
            const user = await User_1.default.findOne({ discordId: member.discordId });
            if (user) {
                totalWealth += user.balance;
            }
        }
        // Calculate activity score (sum of all member milestone achievements)
        const activityScore = members.reduce((sum, member) => sum + member.milestonesAchieved, 0);
        // Calculate age in days
        const ageInDays = Math.floor((Date.now() - dynasty.createdAt.getTime()) / (1000 * 60 * 60 * 24));
        // Calculate new level
        const newLevel = calculateDynastyLevel(totalWealth, activityScore, members.length, ageInDays);
        // Update dynasty
        await Dynasty_1.default.findByIdAndUpdate(dynastyId, {
            totalWealth,
            memberCount: members.length,
            activityScore,
            level: newLevel,
            lastActivityAt: new Date()
        });
        console.log(`[Dynasty Service] Updated stats for dynasty ${dynastyId}: Level ${newLevel}, Wealth ${totalWealth}, Activity ${activityScore}`);
    }
    catch (error) {
        console.error('Error updating dynasty stats:', error);
    }
}
/**
 * Get dynasty leaderboard
 */
async function getDynastyLeaderboard(guildId, sortBy = 'level', limit = 10) {
    try {
        let sortField = { level: -1, totalWealth: -1 };
        switch (sortBy) {
            case 'wealth':
                sortField = { totalWealth: -1, level: -1 };
                break;
            case 'activity':
                sortField = { activityScore: -1, level: -1 };
                break;
            case 'members':
                sortField = { memberCount: -1, level: -1 };
                break;
            default:
                sortField = { level: -1, totalWealth: -1 };
        }
        return await Dynasty_1.default.find({ guildId })
            .sort(sortField)
            .limit(limit);
    }
    catch (error) {
        console.error('Error getting dynasty leaderboard:', error);
        return [];
    }
}
/**
 * Get all dynasties in a guild
 */
async function getAllDynasties(guildId) {
    return await Dynasty_1.default.find({ guildId }).sort({ level: -1, totalWealth: -1 });
}
/**
 * Send dynasty invitation
 */
async function sendDynastyInvitation(dynastyId, inviterId, inviteeId, guildId, message) {
    const session = await mongoose_1.default.startSession();
    try {
        return await session.withTransaction(async () => {
            // Check if inviter has permission
            const inviterMembership = await DynastyMember_1.default.findOne({ dynastyId, discordId: inviterId });
            if (!inviterMembership || !inviterMembership.permissions.canInvite) {
                return { success: false, reason: 'You do not have permission to invite members' };
            }
            // Check if invitee is already in a dynasty
            const existingMembership = await DynastyMember_1.default.findOne({ discordId: inviteeId, guildId });
            if (existingMembership) {
                return { success: false, reason: 'This user is already a member of a dynasty' };
            }
            // Check if there's already a pending invitation
            const existingInvite = await DynastyInvite_1.default.findOne({
                dynastyId,
                inviteeId,
                status: 'pending'
            });
            if (existingInvite) {
                return { success: false, reason: 'This user already has a pending invitation to your dynasty' };
            }
            // Check dynasty member limits
            const dynasty = await Dynasty_1.default.findById(dynastyId);
            if (!dynasty) {
                return { success: false, reason: 'Dynasty not found' };
            }
            if (dynasty.memberCount >= dynasty.maxMembers) {
                return { success: false, reason: 'Dynasty is at maximum member capacity' };
            }
            // Check pending invite limits
            if (dynasty.invitesPending >= exports.DYNASTY_MAX_PENDING_INVITES) {
                return { success: false, reason: 'Dynasty has reached maximum pending invitations limit' };
            }
            // Create invitation
            const expiresAt = new Date(Date.now() + (exports.DYNASTY_INVITE_EXPIRY_HOURS * 60 * 60 * 1000));
            const invite = await DynastyInvite_1.default.create([{
                    dynastyId,
                    guildId,
                    inviterId,
                    inviteeId,
                    status: 'pending',
                    message: message?.trim() || '',
                    expiresAt
                }], { session });
            // Update dynasty pending invites count
            await Dynasty_1.default.findByIdAndUpdate(dynastyId, {
                $inc: { invitesPending: 1 }
            }, { session });
            console.log(`[Dynasty Service] Invitation sent from ${inviterId} to ${inviteeId} for dynasty ${dynastyId}`);
            return { success: true, invite: invite[0] };
        });
    }
    catch (error) {
        console.error('Error sending dynasty invitation:', error);
        return { success: false, reason: 'Failed to send invitation' };
    }
    finally {
        await session.endSession();
    }
}
/**
 * Accept dynasty invitation
 */
async function acceptDynastyInvitation(inviteId, inviteeId, client) {
    const session = await mongoose_1.default.startSession();
    try {
        return await session.withTransaction(async () => {
            // Find and validate invitation
            const invite = await DynastyInvite_1.default.findById(inviteId);
            if (!invite) {
                return { success: false, reason: 'Invitation not found' };
            }
            if (invite.inviteeId !== inviteeId) {
                return { success: false, reason: 'This invitation is not for you' };
            }
            if (invite.status !== 'pending') {
                return { success: false, reason: 'This invitation is no longer valid' };
            }
            if (invite.expiresAt < new Date()) {
                // Mark as expired
                await DynastyInvite_1.default.findByIdAndUpdate(inviteId, { status: 'expired' }, { session });
                return { success: false, reason: 'This invitation has expired' };
            }
            // Check if user is already in a dynasty
            const existingMembership = await DynastyMember_1.default.findOne({
                discordId: inviteeId,
                guildId: invite.guildId
            });
            if (existingMembership) {
                return { success: false, reason: 'You are already a member of a dynasty' };
            }
            // Get dynasty and check capacity
            const dynasty = await Dynasty_1.default.findById(invite.dynastyId);
            if (!dynasty) {
                return { success: false, reason: 'Dynasty no longer exists' };
            }
            if (dynasty.memberCount >= dynasty.maxMembers) {
                return { success: false, reason: 'Dynasty is at maximum capacity' };
            }
            // Create membership
            const member = await DynastyMember_1.default.create([{
                    dynastyId: invite.dynastyId,
                    discordId: inviteeId,
                    guildId: invite.guildId,
                    role: 'member',
                    joinedAt: new Date(),
                    lastActiveAt: new Date(),
                    contributionScore: 0,
                    totalContributed: 0,
                    milestonesAchieved: 0,
                    invitedBy: invite.inviterId,
                    permissions: {
                        canInvite: false,
                        canKick: false,
                        canWithdraw: false,
                        canManageBank: false
                    }
                }], { session });
            // Update invitation status
            await DynastyInvite_1.default.findByIdAndUpdate(inviteId, {
                status: 'accepted',
                respondedAt: new Date()
            }, { session });
            // Update dynasty stats
            await Dynasty_1.default.findByIdAndUpdate(invite.dynastyId, {
                $inc: {
                    memberCount: 1,
                    invitesPending: -1
                },
                lastActivityAt: new Date()
            }, { session });
            console.log(`[Dynasty Service] User ${inviteeId} accepted invitation to dynasty ${invite.dynastyId}`);
            return { success: true, dynasty, member: member[0] };
        });
    }
    catch (error) {
        console.error('Error accepting dynasty invitation:', error);
        return { success: false, reason: 'Failed to accept invitation' };
    }
    finally {
        await session.endSession();
    }
}
/**
 * Decline dynasty invitation
 */
async function declineDynastyInvitation(inviteId, inviteeId) {
    try {
        const invite = await DynastyInvite_1.default.findById(inviteId);
        if (!invite) {
            return { success: false, reason: 'Invitation not found' };
        }
        if (invite.inviteeId !== inviteeId) {
            return { success: false, reason: 'This invitation is not for you' };
        }
        if (invite.status !== 'pending') {
            return { success: false, reason: 'This invitation is no longer valid' };
        }
        // Update invitation status
        await DynastyInvite_1.default.findByIdAndUpdate(inviteId, {
            status: 'declined',
            respondedAt: new Date()
        });
        // Update dynasty pending invites count
        await Dynasty_1.default.findByIdAndUpdate(invite.dynastyId, {
            $inc: { invitesPending: -1 }
        });
        console.log(`[Dynasty Service] User ${inviteeId} declined invitation to dynasty ${invite.dynastyId}`);
        return { success: true };
    }
    catch (error) {
        console.error('Error declining dynasty invitation:', error);
        return { success: false, reason: 'Failed to decline invitation' };
    }
}
/**
 * Get pending invitations for a user
 */
async function getUserPendingInvitations(discordId, guildId) {
    try {
        const invites = await DynastyInvite_1.default.find({
            inviteeId: discordId,
            guildId,
            status: 'pending',
            expiresAt: { $gt: new Date() }
        }).populate('dynastyId');
        return invites;
    }
    catch (error) {
        console.error('Error getting user pending invitations:', error);
        return [];
    }
}
