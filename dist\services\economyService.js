"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adjustBalance = adjustBalance;
exports.getLeaderboard = getLeaderboard;
exports.getTransactionHistory = getTransactionHistory;
exports.ensureUser = ensureUser;
const User_1 = __importDefault(require("../models/User"));
const Transaction_1 = __importDefault(require("../models/Transaction"));
const errorHandler_1 = require("../utils/errorHandler");
const mongoose_1 = __importDefault(require("mongoose"));
const roleAssignmentService_1 = require("./roleAssignmentService");
function logDatabaseOperation(operation, details) {
    console.log(`[Database Operation] ${operation}:`, JSON.stringify(details, null, 2));
}
async function adjustBalance(discordId, amount, type, details, client, guildId, dynastyId) {
    // Input validation
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
        throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
    }
    if (typeof amount !== 'number' || isNaN(amount)) {
        throw new errorHandler_1.DatabaseError('Invalid amount provided');
    }
    const session = await mongoose_1.default.startSession();
    logDatabaseOperation('Starting Transaction', { discordId, amount, type, details });
    try {
        await session.withTransaction(async () => {
            logDatabaseOperation('Finding/Updating User', { discordId, amount });
            // Validate discordId before database operation
            const trimmedDiscordId = discordId.trim();
            if (!trimmedDiscordId) {
                throw new Error('Discord ID cannot be empty after trimming');
            }
            // Atomic update or insert
            const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId }, {
                $inc: { balance: amount },
                $setOnInsert: { discordId: trimmedDiscordId }
            }, {
                new: true,
                upsert: true,
                runValidators: true,
                session
            });
            logDatabaseOperation('Creating Transaction Record', {
                discordId: trimmedDiscordId,
                type,
                amount,
                details
            });
            // Create transaction record in same transaction
            await Transaction_1.default.create([{
                    discordId: trimmedDiscordId,
                    type,
                    amount,
                    details,
                    dynastyId,
                    timestamp: new Date()
                }], { session });
            logDatabaseOperation('Transaction Complete', { userId: user?._id, newBalance: user?.balance });
            // Check for role achievements if balance increased and we have client/guild info
            if (amount > 0 && client && guildId && user) {
                // Schedule role checking after transaction completes
                setImmediate(async () => {
                    try {
                        const roleResult = await (0, roleAssignmentService_1.checkAndAssignRoles)(client, trimmedDiscordId, guildId, user.balance);
                        if (roleResult) {
                            await (0, roleAssignmentService_1.sendRoleAchievementNotifications)(roleResult, client);
                        }
                    }
                    catch (error) {
                        console.error('Error checking roles after balance adjustment:', error);
                    }
                });
            }
            return user;
        });
    }
    catch (error) {
        console.error('Error in adjustBalance:', error);
        if (error instanceof Error && error.name === 'ValidationError') {
            throw new errorHandler_1.DatabaseError('Transaction validation failed', error);
        }
        else if (error instanceof Error && error.name === 'MongoServerError') {
            const err = error;
            if (err.code === 11000) {
                throw new errorHandler_1.DatabaseError('Transaction conflict detected', error);
            }
            throw new errorHandler_1.DatabaseError('Database operation failed', error);
        }
        else if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Transaction processing failed', error);
        }
        throw new errorHandler_1.DatabaseError('Unexpected error during transaction');
    }
    finally {
        await session.endSession();
    }
}
async function getLeaderboard(limit = 10) {
    try {
        logDatabaseOperation('Fetching Leaderboard', { limit });
        const users = await User_1.default.find().sort({ balance: -1 }).limit(limit);
        logDatabaseOperation('Leaderboard Fetched', { count: users.length });
        return users;
    }
    catch (error) {
        if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Failed to fetch leaderboard', error);
        }
        throw new errorHandler_1.DatabaseError('Failed to fetch leaderboard');
    }
}
async function getTransactionHistory(discordId, limit = 10) {
    try {
        logDatabaseOperation('Fetching Transaction History', { discordId, limit });
        const transactions = await Transaction_1.default.find({ discordId }).sort({ timestamp: -1 }).limit(limit);
        logDatabaseOperation('Transaction History Fetched', { count: transactions.length });
        return transactions;
    }
    catch (error) {
        if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Failed to fetch transaction history', error);
        }
        throw new errorHandler_1.DatabaseError('Failed to fetch transaction history');
    }
}
// Helper function to ensure user exists
async function ensureUser(discordId) {
    // Input validation
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
        throw new errorHandler_1.DatabaseError('Invalid Discord ID provided to ensureUser');
    }
    try {
        const trimmedDiscordId = discordId.trim();
        logDatabaseOperation('Ensuring User Exists', { discordId: trimmedDiscordId });
        const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId }, { $setOnInsert: { discordId: trimmedDiscordId, balance: 0 } }, {
            upsert: true,
            new: true,
            runValidators: true
        });
        logDatabaseOperation('User Ensured', { userId: user._id, isNew: user.isNew });
        return user;
    }
    catch (error) {
        if (error instanceof Error && error.name === 'ValidationError') {
            throw new errorHandler_1.DatabaseError('Invalid user data format', error);
        }
        else if (error instanceof Error && error.name === 'MongoServerError' && error.code === 11000) {
            throw new errorHandler_1.DatabaseError('User already exists', error);
        }
        else if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Failed to create/fetch user', error);
        }
        throw new errorHandler_1.DatabaseError('Unexpected error while creating/fetching user');
    }
}
