"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const economyService_1 = require("../services/economyService");
const embedBuilder_1 = require("../utils/embedBuilder");
const dynastyService_1 = require("../services/dynastyService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('balance')
        .setDescription('Check your Phalanx Loyalty Coin balance'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            const discordId = interaction.user.id;
            // Use ensureUser helper to handle user creation/fetch
            const user = await (0, economyService_1.ensureUser)(discordId);
            // Check for dynasty membership
            const guildId = interaction.guild?.id;
            let dynastyInfo = '';
            if (guildId) {
                try {
                    const membership = await (0, dynastyService_1.getUserDynastyMembership)(discordId, guildId);
                    if (membership) {
                        const dynasty = await (0, dynastyService_1.getDynastyById)(membership.dynastyId);
                        if (dynasty) {
                            const roleEmoji = membership.role === 'founder' ? '👑' : membership.role === 'council' ? '⭐' : '🛡️';
                            dynastyInfo = `\n${roleEmoji} **Dynasty:** ${dynasty.name} (Level ${dynasty.level})`;
                        }
                    }
                }
                catch (error) {
                    console.error('Error fetching dynasty info for balance:', error);
                }
            }
            // Create rich embed with user information
            const embed = (0, embedBuilder_1.createEconomyEmbed)('Your Balance')
                .setDescription(`${(0, embedBuilder_1.formatCoins)(user.balance)}${dynastyInfo}\n\n${embedBuilder_1.EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb the leaderboard!*`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.BANK} Account Status`,
                value: user.balance >= 1000 ?
                    `${embedBuilder_1.EMOJIS.SUCCESS.CROWN} **Premium Member**` :
                    `${embedBuilder_1.EMOJIS.ECONOMY.CHART} **Growing Account**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CALENDAR} Last Updated`,
                value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
                inline: true
            })
                .setFooter({
                text: 'Use /help to see ways to earn more coins!'
            });
            // Add user info to embed
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            // Create quick action buttons
            const actionButtons = (0, embedBuilder_1.createQuickActionButtons)();
            await interaction.reply({
                embeds: [embed],
                components: [actionButtons],
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof Error && error.name === 'ValidationError') {
                throw new errorHandler_1.DatabaseError('Your user profile data is invalid', error);
            }
            else if (error instanceof Error && error.name === 'MongoServerError') {
                const err = error;
                if (err.code === 11000) {
                    throw new errorHandler_1.DatabaseError('Your profile is being updated elsewhere. Please try again.', error);
                }
                throw new errorHandler_1.DatabaseError('Database operation failed', error);
            }
            else if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError('Failed to fetch your balance', error);
            }
            else {
                throw new errorHandler_1.DatabaseError('An unexpected error occurred while checking your balance');
            }
        }
    })
};
