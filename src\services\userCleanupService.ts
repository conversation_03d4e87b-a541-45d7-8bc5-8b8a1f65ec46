import { GuildMember, PartialGuildMember } from 'discord.js';
import User from '../models/User';
import Transaction from '../models/Transaction';
import { ReactionReward } from '../models/ReactionReward';
import { DatabaseError } from '../utils/errorHandler';
import mongoose from 'mongoose';

export interface CleanupResult {
    success: boolean;
    userDataRemoved: boolean;
    transactionsRemoved: number;
    reactionRewardsRemoved: number;
    errors: string[];
    timeTaken: number;
}

/**
 * Comprehensive cleanup service for removing user data when members leave the server
 */
export class UserCleanupService {
    private static readonly CLEANUP_TIMEOUT_MS = 30000; // 30 seconds timeout
    private static readonly GRACE_PERIOD_MS = 5000; // 5 second grace period before cleanup

    /**
     * Main cleanup function that removes all user data when a member leaves
     */
    public static async cleanupUserData(member: GuildMember | PartialGuildMember): Promise<CleanupResult> {
        const startTime = Date.now();
        const result: CleanupResult = {
            success: false,
            userDataRemoved: false,
            transactionsRemoved: 0,
            reactionRewardsRemoved: 0,
            errors: [],
            timeTaken: 0
        };

        const userId = member.user?.id;
        const guildName = member.guild?.name || 'Unknown Guild';
        const userName = member.displayName || member.user?.username || 'Unknown User';

        if (!userId) {
            const errorMsg = 'No user ID available for cleanup';
            result.errors.push(errorMsg);
            console.error(`[User Cleanup] ${errorMsg}`);
            result.timeTaken = Date.now() - startTime;
            return result;
        }

        console.log(`[User Cleanup] Starting cleanup for user ${userName} (${userId}) who left ${guildName}`);

        try {
            // Add grace period to prevent accidental cleanup if user rejoins quickly
            await new Promise(resolve => setTimeout(resolve, this.GRACE_PERIOD_MS));

            // Check if user has rejoined during grace period (only if we have guild access)
            if (member.guild) {
                const stillInGuild = member.guild.members.cache.has(userId);
                if (stillInGuild) {
                    console.log(`[User Cleanup] User ${userName} rejoined during grace period, skipping cleanup`);
                    result.success = true;
                    result.timeTaken = Date.now() - startTime;
                    return result;
                }
            }

            // Start database session for transaction
            const session = await mongoose.startSession();
            
            try {
                await session.withTransaction(async () => {
                    // 1. Remove user balance record
                    const userDeleteResult = await User.deleteOne({ discordId: userId }).session(session);
                    result.userDataRemoved = userDeleteResult.deletedCount > 0;
                    
                    if (result.userDataRemoved) {
                        console.log(`[User Cleanup] Removed user balance record for ${userName}`);
                    }

                    // 2. Remove transaction history
                    const transactionDeleteResult = await Transaction.deleteMany({ discordId: userId }).session(session);
                    result.transactionsRemoved = transactionDeleteResult.deletedCount;
                    
                    if (result.transactionsRemoved > 0) {
                        console.log(`[User Cleanup] Removed ${result.transactionsRemoved} transaction records for ${userName}`);
                    }

                    // 3. Remove reaction reward records
                    const reactionRewardDeleteResult = await ReactionReward.deleteMany({ userId }).session(session);
                    result.reactionRewardsRemoved = reactionRewardDeleteResult.deletedCount;

                    if (result.reactionRewardsRemoved > 0) {
                        console.log(`[User Cleanup] Removed ${result.reactionRewardsRemoved} reaction reward records for ${userName}`);
                    }

                    // 4. Handle Dynasty membership cleanup
                    let dynastyRecordsRemoved = 0;
                    try {
                        const DynastyMember = require('../models/DynastyMember').default;
                        const DynastyInvite = require('../models/DynastyInvite').default;
                        const Dynasty = require('../models/Dynasty').default;
                        const DynastyTransaction = require('../models/DynastyTransaction').default;

                        // Find user's dynasty membership
                        const membership = await DynastyMember.findOne({ discordId: userId, guildId: member.guild.id }).session(session);

                        if (membership) {
                            // If user is founder, handle dynasty dissolution or transfer
                            if (membership.role === 'founder') {
                                const dynasty = await Dynasty.findById(membership.dynastyId).session(session);
                                if (dynasty) {
                                    // Find council members to potentially transfer leadership
                                    const councilMembers = await DynastyMember.find({
                                        dynastyId: membership.dynastyId,
                                        role: 'council'
                                    }).session(session);

                                    if (councilMembers.length > 0) {
                                        // Transfer leadership to first council member
                                        await DynastyMember.findByIdAndUpdate(councilMembers[0]._id, {
                                            role: 'founder'
                                        }).session(session);
                                        console.log(`[User Cleanup] Transferred dynasty leadership from ${userName} to council member`);
                                    } else {
                                        // No council members, check for regular members
                                        const regularMembers = await DynastyMember.find({
                                            dynastyId: membership.dynastyId,
                                            role: 'member'
                                        }).session(session);

                                        if (regularMembers.length > 0) {
                                            // Promote first member to founder
                                            await DynastyMember.findByIdAndUpdate(regularMembers[0]._id, {
                                                role: 'founder'
                                            }).session(session);
                                            console.log(`[User Cleanup] Promoted member to founder after ${userName} left`);
                                        } else {
                                            // No other members, dissolve dynasty
                                            await Dynasty.findByIdAndDelete(membership.dynastyId).session(session);
                                            console.log(`[User Cleanup] Dissolved empty dynasty after founder ${userName} left`);
                                        }
                                    }
                                }
                            }

                            // Remove user's dynasty membership
                            await DynastyMember.findByIdAndDelete(membership._id).session(session);
                            dynastyRecordsRemoved++;

                            // Update dynasty member count
                            await Dynasty.findByIdAndUpdate(membership.dynastyId, {
                                $inc: { memberCount: -1 }
                            }).session(session);
                        }

                        // Remove any pending invitations sent by or to this user
                        const inviteDeleteResult = await DynastyInvite.deleteMany({
                            $or: [
                                { inviterId: userId },
                                { inviteeId: userId }
                            ],
                            guildId: member.guild.id
                        }).session(session);
                        dynastyRecordsRemoved += inviteDeleteResult.deletedCount;

                        // Remove dynasty transaction records
                        const dynastyTransactionDeleteResult = await DynastyTransaction.deleteMany({
                            discordId: userId,
                            guildId: member.guild.id
                        }).session(session);
                        dynastyRecordsRemoved += dynastyTransactionDeleteResult.deletedCount;

                        if (dynastyRecordsRemoved > 0) {
                            console.log(`[User Cleanup] Removed ${dynastyRecordsRemoved} dynasty-related records for ${userName}`);
                        }
                    } catch (dynastyError) {
                        console.error(`[User Cleanup] Error cleaning dynasty data for ${userName}:`, dynastyError);
                        // Don't fail the entire cleanup if dynasty cleanup fails
                    }

                    // Log summary
                    const totalRecordsRemoved = (result.userDataRemoved ? 1 : 0) + result.transactionsRemoved + result.reactionRewardsRemoved + dynastyRecordsRemoved;
                    console.log(`[User Cleanup] Successfully removed ${totalRecordsRemoved} total records for ${userName} from ${guildName}`);
                });

                result.success = true;

            } catch (error) {
                const errorMsg = `Database transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                result.errors.push(errorMsg);
                console.error(`[User Cleanup] ${errorMsg}`);
                throw error;
            } finally {
                await session.endSession();
            }

        } catch (error) {
            const errorMsg = `Cleanup failed for user ${userName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            result.errors.push(errorMsg);
            console.error(`[User Cleanup] ${errorMsg}`);
            result.success = false;
        }

        result.timeTaken = Date.now() - startTime;
        
        // Log final result
        if (result.success) {
            console.log(`[User Cleanup] Completed cleanup for ${userName} in ${result.timeTaken}ms`);
        } else {
            console.error(`[User Cleanup] Failed cleanup for ${userName} after ${result.timeTaken}ms:`, result.errors);
        }

        return result;
    }

    /**
     * Check if user has any data that would be cleaned up (for testing/verification)
     */
    public static async checkUserData(userId: string): Promise<{
        hasUserRecord: boolean;
        transactionCount: number;
        reactionRewardCount: number;
    }> {
        try {
            const [userRecord, transactionCount, reactionRewardCount] = await Promise.all([
                User.findOne({ discordId: userId }),
                Transaction.countDocuments({ discordId: userId }),
                ReactionReward.countDocuments({ userId })
            ]);

            return {
                hasUserRecord: !!userRecord,
                transactionCount,
                reactionRewardCount
            };
        } catch (error) {
            console.error(`[User Cleanup] Failed to check user data for ${userId}:`, error);
            throw new DatabaseError(`Failed to check user data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Cleanup orphaned data (for maintenance - removes data for users not in any guild)
     * This is a separate maintenance function, not part of the member leave cleanup
     */
    public static async cleanupOrphanedData(): Promise<{
        orphanedUsers: number;
        orphanedTransactions: number;
        orphanedReactionRewards: number;
    }> {
        console.log('[User Cleanup] Starting orphaned data cleanup...');
        
        try {
            // This would require checking against all guilds the bot is in
            // For now, we'll just log that this function exists for future implementation
            console.log('[User Cleanup] Orphaned data cleanup not yet implemented - requires guild membership verification');
            
            return {
                orphanedUsers: 0,
                orphanedTransactions: 0,
                orphanedReactionRewards: 0
            };
        } catch (error) {
            console.error('[User Cleanup] Orphaned data cleanup failed:', error);
            throw new DatabaseError(`Orphaned data cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
