import { SlashCommandBuilder, ChatInputCommandInteraction } from 'discord.js';
import User from '../models/User';
import { withError<PERSON>and<PERSON>, DatabaseError } from '../utils/errorHandler';
import { ensureUser } from '../services/economyService';
import { createEconomyEmbed, addUserInfo, formatCoins, createQuickActionButtons, EMOJIS } from '../utils/embedBuilder';
import { getUserDynastyMembership, getDynastyById } from '../services/dynastyService';
import mongoose from 'mongoose';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('balance')
    .setDescription('Check your Phalanx Loyalty Coin balance'),
  execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
    try {
      const discordId = interaction.user.id;

      // Use ensureUser helper to handle user creation/fetch
      const user = await ensureUser(discordId);

      // Check for dynasty membership
      const guildId = interaction.guild?.id;
      let dynastyInfo = '';
      if (guildId) {
        try {
          const membership = await getUserDynastyMembership(discordId, guildId);
          if (membership) {
            const dynasty = await getDynastyById(membership.dynastyId);
            if (dynasty) {
              const roleEmoji = membership.role === 'founder' ? '👑' : membership.role === 'council' ? '⭐' : '🛡️';
              dynastyInfo = `\n${roleEmoji} **Dynasty:** ${dynasty.name} (Level ${dynasty.level})`;
            }
          }
        } catch (error) {
          console.error('Error fetching dynasty info for balance:', error);
        }
      }

      // Create rich embed with user information
      const embed = createEconomyEmbed('Your Balance')
        .setDescription(`${formatCoins(user.balance)}${dynastyInfo}\n\n${EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb the leaderboard!*`)
        .addFields(
          {
            name: `${EMOJIS.ECONOMY.BANK} Account Status`,
            value: user.balance >= 1000 ?
              `${EMOJIS.SUCCESS.CROWN} **Premium Member**` :
              `${EMOJIS.ECONOMY.CHART} **Growing Account**`,
            inline: true
          },
          {
            name: `${EMOJIS.MISC.CALENDAR} Last Updated`,
            value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
            inline: true
          }
        )
        .setFooter({
          text: 'Use /help to see ways to earn more coins!'
        });

      // Add user info to embed
      addUserInfo(embed, interaction.user);

      // Create quick action buttons
      const actionButtons = createQuickActionButtons();

      await interaction.reply({
        embeds: [embed],
        components: [actionButtons],
        ephemeral: false
      });
    } catch (error: unknown) {
      if (error instanceof Error && error.name === 'ValidationError') {
        throw new DatabaseError('Your user profile data is invalid', error);
      } else if (error instanceof Error && error.name === 'MongoServerError') {
        const err = error as any;
        if (err.code === 11000) {
          throw new DatabaseError('Your profile is being updated elsewhere. Please try again.', error);
        }
        throw new DatabaseError('Database operation failed', error);
      } else if (error instanceof Error) {
        throw new DatabaseError('Failed to fetch your balance', error);
      } else {
        throw new DatabaseError('An unexpected error occurred while checking your balance');
      }
    }
  })
};
