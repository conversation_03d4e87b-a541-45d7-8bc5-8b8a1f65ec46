"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const dynastyMemberSchema = new mongoose_1.Schema({
    dynastyId: {
        type: String,
        required: [true, 'Dynasty ID is required'],
        index: true
    },
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    role: {
        type: String,
        enum: ['founder', 'council', 'member'],
        default: 'member',
        required: [true, 'Role is required']
    },
    joinedAt: {
        type: Date,
        default: Date.now
    },
    lastActiveAt: {
        type: Date,
        default: Date.now
    },
    contributionScore: {
        type: Number,
        default: 0,
        min: [0, 'Contribution score cannot be negative']
    },
    totalContributed: {
        type: Number,
        default: 0,
        min: [0, 'Total contributed cannot be negative']
    },
    milestonesAchieved: {
        type: Number,
        default: 0,
        min: [0, 'Milestones achieved cannot be negative']
    },
    invitedBy: {
        type: String,
        index: true
    },
    permissions: {
        canInvite: {
            type: Boolean,
            default: false
        },
        canKick: {
            type: Boolean,
            default: false
        },
        canWithdraw: {
            type: Boolean,
            default: false
        },
        canManageBank: {
            type: Boolean,
            default: false
        }
    }
}, {
    timestamps: true
});
// Compound indexes for efficient queries
dynastyMemberSchema.index({ dynastyId: 1, discordId: 1 }, { unique: true });
dynastyMemberSchema.index({ guildId: 1, discordId: 1 });
dynastyMemberSchema.index({ dynastyId: 1, role: 1 });
dynastyMemberSchema.index({ dynastyId: 1, contributionScore: -1 });
dynastyMemberSchema.index({ lastActiveAt: 1 });
// Pre-save middleware to set permissions based on role
dynastyMemberSchema.pre('save', function (next) {
    if (this.isModified('role')) {
        switch (this.role) {
            case 'founder':
                this.permissions = {
                    canInvite: true,
                    canKick: true,
                    canWithdraw: true,
                    canManageBank: true
                };
                break;
            case 'council':
                this.permissions = {
                    canInvite: true,
                    canKick: false,
                    canWithdraw: true,
                    canManageBank: false
                };
                break;
            case 'member':
                this.permissions = {
                    canInvite: false,
                    canKick: false,
                    canWithdraw: false,
                    canManageBank: false
                };
                break;
        }
    }
    next();
});
exports.default = (0, mongoose_1.model)('DynastyMember', dynastyMemberSchema);
