"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MilestoneAuditService = exports.MilestoneAuditLog = void 0;
const mongoose_1 = require("mongoose");
const milestoneAuditLogSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    userId: {
        type: String,
        index: true
    },
    adminId: {
        type: String,
        index: true
    },
    action: {
        type: String,
        enum: ['milestone_achieved', 'config_created', 'config_updated', 'config_deleted', 'system_enabled', 'system_disabled', 'suspicious_activity', 'rate_limit_hit', 'blacklist_added', 'blacklist_removed'],
        required: [true, 'Action is required'],
        index: true
    },
    category: {
        type: String,
        enum: ['achievement', 'admin', 'system', 'security'],
        required: [true, 'Category is required'],
        index: true
    },
    details: {
        type: String,
        required: [true, 'Details are required']
    },
    metadata: {
        milestoneType: String,
        rewardAmount: Number,
        oldValue: mongoose_1.Schema.Types.Mixed,
        newValue: mongoose_1.Schema.Types.Mixed,
        ipAddress: String,
        userAgent: String,
        suspiciousReason: String,
        rateLimitType: String
    },
    timestamp: {
        type: Date,
        default: Date.now,
        index: true
    },
    severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'low',
        index: true
    }
}, {
    timestamps: { createdAt: 'timestamp', updatedAt: false }
});
// Compound indexes for efficient queries
milestoneAuditLogSchema.index({ guildId: 1, timestamp: -1 });
milestoneAuditLogSchema.index({ guildId: 1, category: 1, timestamp: -1 });
milestoneAuditLogSchema.index({ guildId: 1, severity: 1, timestamp: -1 });
milestoneAuditLogSchema.index({ userId: 1, action: 1, timestamp: -1 });
exports.MilestoneAuditLog = (0, mongoose_1.model)('MilestoneAuditLog', milestoneAuditLogSchema);
/**
 * Milestone Audit Service for comprehensive logging and monitoring
 */
class MilestoneAuditService {
    /**
     * Logs a milestone achievement
     */
    static async logMilestoneAchievement(guildId, userId, milestoneType, rewardAmount, details, client) {
        try {
            await exports.MilestoneAuditLog.create({
                guildId,
                userId,
                action: 'milestone_achieved',
                category: 'achievement',
                details,
                metadata: {
                    milestoneType,
                    rewardAmount
                },
                severity: 'low'
            });
            console.log(`[Milestone Audit] Achievement logged: ${userId} earned ${milestoneType} for ${rewardAmount} PLC`);
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to log achievement:', error);
        }
    }
    /**
     * Logs admin configuration changes
     */
    static async logAdminAction(guildId, adminId, action, details, metadata) {
        try {
            await exports.MilestoneAuditLog.create({
                guildId,
                adminId,
                action,
                category: 'admin',
                details,
                metadata: metadata || {},
                severity: 'medium'
            });
            console.log(`[Milestone Audit] Admin action logged: ${adminId} performed ${action}`);
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to log admin action:', error);
        }
    }
    /**
     * Logs suspicious activity
     */
    static async logSuspiciousActivity(guildId, userId, reason, details, metadata) {
        try {
            await exports.MilestoneAuditLog.create({
                guildId,
                userId,
                action: 'suspicious_activity',
                category: 'security',
                details,
                metadata: {
                    suspiciousReason: reason,
                    ...metadata
                },
                severity: 'high'
            });
            console.warn(`[Milestone Audit] Suspicious activity logged: ${userId} - ${reason}`);
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to log suspicious activity:', error);
        }
    }
    /**
     * Logs rate limit hits
     */
    static async logRateLimitHit(guildId, userId, rateLimitType, details) {
        try {
            await exports.MilestoneAuditLog.create({
                guildId,
                userId,
                action: 'rate_limit_hit',
                category: 'security',
                details,
                metadata: {
                    rateLimitType
                },
                severity: 'medium'
            });
            console.log(`[Milestone Audit] Rate limit hit logged: ${userId} - ${rateLimitType}`);
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to log rate limit hit:', error);
        }
    }
    /**
     * Logs blacklist actions
     */
    static async logBlacklistAction(guildId, userId, action, reason, adminId) {
        try {
            await exports.MilestoneAuditLog.create({
                guildId,
                userId,
                adminId,
                action,
                category: 'security',
                details: `User ${action.replace('_', ' ')} - ${reason}`,
                metadata: {
                    suspiciousReason: reason
                },
                severity: 'critical'
            });
            console.warn(`[Milestone Audit] Blacklist action logged: ${userId} - ${action}`);
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to log blacklist action:', error);
        }
    }
    /**
     * Gets audit logs for a guild with filtering options
     */
    static async getAuditLogs(guildId, options = {}) {
        try {
            const query = { guildId };
            if (options.category)
                query.category = options.category;
            if (options.action)
                query.action = options.action;
            if (options.userId)
                query.userId = options.userId;
            if (options.adminId)
                query.adminId = options.adminId;
            if (options.severity)
                query.severity = options.severity;
            if (options.startDate || options.endDate) {
                query.timestamp = {};
                if (options.startDate)
                    query.timestamp.$gte = options.startDate;
                if (options.endDate)
                    query.timestamp.$lte = options.endDate;
            }
            return await exports.MilestoneAuditLog.find(query)
                .sort({ timestamp: -1 })
                .limit(options.limit || 100);
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to get audit logs:', error);
            return [];
        }
    }
    /**
     * Gets security alerts (high and critical severity logs)
     */
    static async getSecurityAlerts(guildId, hours = 24) {
        try {
            const cutoffDate = new Date(Date.now() - (hours * 60 * 60 * 1000));
            return await exports.MilestoneAuditLog.find({
                guildId,
                severity: { $in: ['high', 'critical'] },
                timestamp: { $gte: cutoffDate }
            }).sort({ timestamp: -1 });
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to get security alerts:', error);
            return [];
        }
    }
    /**
     * Gets user activity summary from audit logs
     */
    static async getUserActivitySummary(guildId, userId, days = 7) {
        try {
            const cutoffDate = new Date(Date.now() - (days * 24 * 60 * 60 * 1000));
            const [achievements, suspicious, rateLimits, recent] = await Promise.all([
                exports.MilestoneAuditLog.find({
                    guildId,
                    userId,
                    action: 'milestone_achieved',
                    timestamp: { $gte: cutoffDate }
                }),
                exports.MilestoneAuditLog.countDocuments({
                    guildId,
                    userId,
                    action: 'suspicious_activity',
                    timestamp: { $gte: cutoffDate }
                }),
                exports.MilestoneAuditLog.countDocuments({
                    guildId,
                    userId,
                    action: 'rate_limit_hit',
                    timestamp: { $gte: cutoffDate }
                }),
                exports.MilestoneAuditLog.find({
                    guildId,
                    userId,
                    timestamp: { $gte: cutoffDate }
                }).sort({ timestamp: -1 }).limit(10)
            ]);
            const totalRewards = achievements.reduce((sum, log) => sum + (log.metadata.rewardAmount || 0), 0);
            return {
                totalAchievements: achievements.length,
                totalRewards,
                suspiciousActivities: suspicious,
                rateLimitHits: rateLimits,
                recentActions: recent
            };
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to get user activity summary:', error);
            return {
                totalAchievements: 0,
                totalRewards: 0,
                suspiciousActivities: 0,
                rateLimitHits: 0,
                recentActions: []
            };
        }
    }
    /**
     * Cleans up old audit logs (older than specified days)
     */
    static async cleanupOldLogs(days = 90) {
        try {
            const cutoffDate = new Date(Date.now() - (days * 24 * 60 * 60 * 1000));
            const result = await exports.MilestoneAuditLog.deleteMany({
                timestamp: { $lt: cutoffDate },
                severity: { $in: ['low', 'medium'] } // Keep high and critical logs longer
            });
            console.log(`[Milestone Audit] Cleaned up ${result.deletedCount} old audit logs`);
            return result.deletedCount;
        }
        catch (error) {
            console.error('[Milestone Audit] Failed to cleanup old logs:', error);
            return 0;
        }
    }
}
exports.MilestoneAuditService = MilestoneAuditService;
