import { Schema, model, Document } from 'mongoose';

export interface IDynastyInvite extends Document {
    dynastyId: string;
    guildId: string;
    inviterId: string; // Discord ID of who sent the invite
    inviteeId: string; // Discord ID of who was invited
    status: 'pending' | 'accepted' | 'declined' | 'expired' | 'cancelled';
    message?: string; // Optional message from inviter
    expiresAt: Date;
    respondedAt?: Date;
    createdAt: Date;
}

const dynastyInviteSchema = new Schema<IDynastyInvite>({
    dynastyId: {
        type: String,
        required: [true, 'Dynasty ID is required'],
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    inviterId: {
        type: String,
        required: [true, 'Inviter ID is required'],
        index: true
    },
    inviteeId: {
        type: String,
        required: [true, 'Invitee ID is required'],
        index: true
    },
    status: {
        type: String,
        enum: ['pending', 'accepted', 'declined', 'expired', 'cancelled'],
        default: 'pending',
        required: [true, 'Status is required'],
        index: true
    },
    message: {
        type: String,
        maxlength: [200, 'Message cannot exceed 200 characters']
    },
    expiresAt: {
        type: Date,
        required: [true, 'Expiration date is required'],
        index: true
    },
    respondedAt: {
        type: Date
    }
}, {
    timestamps: { createdAt: true, updatedAt: false }
});

// Compound indexes for efficient queries
dynastyInviteSchema.index({ dynastyId: 1, inviteeId: 1, status: 1 });
dynastyInviteSchema.index({ inviteeId: 1, status: 1 });
dynastyInviteSchema.index({ expiresAt: 1, status: 1 });
dynastyInviteSchema.index({ guildId: 1, inviterId: 1 });

// Pre-save middleware to set respondedAt when status changes from pending
dynastyInviteSchema.pre('save', function(next) {
    if (this.isModified('status') && this.status !== 'pending' && !this.respondedAt) {
        this.respondedAt = new Date();
    }
    next();
});

export default model<IDynastyInvite>('DynastyInvite', dynastyInviteSchema);
