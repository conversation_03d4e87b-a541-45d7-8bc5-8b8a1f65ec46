import Dynasty, { IDynasty } from '../models/Dynasty';
import DynastyM<PERSON>ber, { IDynastyMember } from '../models/DynastyMember';
import DynastyTransaction from '../models/DynastyTransaction';
import DynastyInvite from '../models/DynastyInvite';
import User from '../models/User';
import { adjustBalance } from './economyService';
import { DatabaseError } from '../utils/errorHandler';
import mongoose from 'mongoose';
import { Client, Guild, GuildMember } from 'discord.js';

// Constants
export const DYNASTY_CREATION_COST = 1000;
export const DYNASTY_MIN_BALANCE = 5000;
export const DYNASTY_MIN_SERVER_DAYS = 30;
export const DYNASTY_INVITE_EXPIRY_HOURS = 24;
export const DYNASTY_MAX_PENDING_INVITES = 5;

// Reserved dynasty names (case-insensitive)
const RESERVED_NAMES = [
    'admin', 'administrator', 'mod', 'moderator', 'staff', 'bot', 'system',
    'phalanx', 'order', 'dynasty', 'guild', 'clan', 'team', 'group',
    'everyone', 'here', 'null', 'undefined', 'test', 'debug'
];

// Profanity filter (basic implementation - can be expanded)
const PROFANITY_WORDS = [
    'damn', 'hell', 'crap', 'stupid', 'idiot', 'moron', 'dumb'
    // Add more as needed
];

/**
 * Check if a user is eligible to create a dynasty
 */
export async function checkDynastyCreationEligibility(
    discordId: string,
    guildId: string,
    client: Client
): Promise<{ eligible: boolean; reason?: string }> {
    try {
        // Check if user already has a dynasty
        const existingMembership = await DynastyMember.findOne({
            discordId,
            guildId
        });

        if (existingMembership) {
            return { eligible: false, reason: 'You are already a member of a dynasty' };
        }

        // Check user balance
        const user = await User.findOne({ discordId });
        if (!user || user.balance < DYNASTY_MIN_BALANCE) {
            return { 
                eligible: false, 
                reason: `You need at least ${DYNASTY_MIN_BALANCE} PLC to create a dynasty (current: ${user?.balance || 0} PLC)` 
            };
        }

        // Check server tenure
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            return { eligible: false, reason: 'Guild not found' };
        }

        const member = await guild.members.fetch(discordId).catch(() => null);
        if (!member) {
            return { eligible: false, reason: 'You are not a member of this server' };
        }

        const joinDate = member.joinedAt;
        if (!joinDate) {
            return { eligible: false, reason: 'Unable to determine your join date' };
        }

        const daysSinceJoin = Math.floor((Date.now() - joinDate.getTime()) / (1000 * 60 * 60 * 24));
        if (daysSinceJoin < DYNASTY_MIN_SERVER_DAYS) {
            return { 
                eligible: false, 
                reason: `You must be a server member for at least ${DYNASTY_MIN_SERVER_DAYS} days (current: ${daysSinceJoin} days)` 
            };
        }

        return { eligible: true };
    } catch (error) {
        console.error('Error checking dynasty creation eligibility:', error);
        throw new DatabaseError('Failed to check dynasty creation eligibility');
    }
}

/**
 * Validate dynasty name
 */
export function validateDynastyName(name: string): { valid: boolean; reason?: string } {
    // Trim and check length
    const trimmedName = name.trim();
    if (trimmedName.length < 3) {
        return { valid: false, reason: 'Dynasty name must be at least 3 characters long' };
    }
    if (trimmedName.length > 20) {
        return { valid: false, reason: 'Dynasty name cannot exceed 20 characters' };
    }

    // Check allowed characters
    if (!/^[a-zA-Z0-9\s\-_]+$/.test(trimmedName)) {
        return { valid: false, reason: 'Dynasty name can only contain letters, numbers, spaces, hyphens, and underscores' };
    }

    // Check reserved names
    if (RESERVED_NAMES.includes(trimmedName.toLowerCase())) {
        return { valid: false, reason: 'This name is reserved and cannot be used' };
    }

    // Check profanity
    const lowerName = trimmedName.toLowerCase();
    for (const word of PROFANITY_WORDS) {
        if (lowerName.includes(word)) {
            return { valid: false, reason: 'Dynasty name contains inappropriate content' };
        }
    }

    return { valid: true };
}

/**
 * Check if dynasty name is available in guild
 */
export async function isDynastyNameAvailable(name: string, guildId: string): Promise<boolean> {
    const existingDynasty = await Dynasty.findOne({
        guildId,
        name: { $regex: new RegExp(`^${name}$`, 'i') } // Case-insensitive check
    });
    return !existingDynasty;
}

/**
 * Create a new dynasty
 */
export async function createDynasty(
    founderId: string,
    guildId: string,
    name: string,
    description: string = '',
    client: Client
): Promise<{ dynasty: IDynasty; member: IDynastyMember }> {
    const session = await mongoose.startSession();
    
    try {
        return await session.withTransaction(async () => {
            // Validate eligibility
            const eligibility = await checkDynastyCreationEligibility(founderId, guildId, client);
            if (!eligibility.eligible) {
                throw new DatabaseError(eligibility.reason || 'Not eligible to create dynasty');
            }

            // Validate name
            const nameValidation = validateDynastyName(name);
            if (!nameValidation.valid) {
                throw new DatabaseError(nameValidation.reason || 'Invalid dynasty name');
            }

            // Check name availability
            const nameAvailable = await isDynastyNameAvailable(name, guildId);
            if (!nameAvailable) {
                throw new DatabaseError('A dynasty with this name already exists');
            }

            // Deduct creation cost
            await adjustBalance(
                founderId,
                -DYNASTY_CREATION_COST,
                'dynasty_creation',
                `Dynasty creation fee for "${name}"`,
                client,
                guildId
            );

            // Create dynasty
            const dynasty = await Dynasty.create([{
                guildId,
                name: name.trim(),
                founderId,
                description: description.trim(),
                level: 1,
                totalWealth: 0,
                memberCount: 1,
                activityScore: 0,
                bankBalance: 0,
                taxRate: 0,
                maxMembers: 20,
                councilWithdrawalLimit: 100,
                invitesPending: 0,
                lastActivityAt: new Date(),
                settings: {
                    publicProfile: true,
                    autoAcceptInvites: false,
                    requireApprovalForWithdrawals: false
                }
            }], { session });

            // Create founder membership
            const member = await DynastyMember.create([{
                dynastyId: dynasty[0]._id.toString(),
                discordId: founderId,
                guildId,
                role: 'founder',
                joinedAt: new Date(),
                lastActiveAt: new Date(),
                contributionScore: 0,
                totalContributed: 0,
                milestonesAchieved: 0,
                permissions: {
                    canInvite: true,
                    canKick: true,
                    canWithdraw: true,
                    canManageBank: true
                }
            }], { session });

            console.log(`[Dynasty Service] Created dynasty "${name}" by ${founderId} in guild ${guildId}`);
            
            return { dynasty: dynasty[0], member: member[0] };
        });
    } catch (error) {
        console.error('Error creating dynasty:', error);
        if (error instanceof DatabaseError) {
            throw error;
        }
        throw new DatabaseError('Failed to create dynasty');
    } finally {
        await session.endSession();
    }
}

/**
 * Get dynasty by ID
 */
export async function getDynastyById(dynastyId: string): Promise<IDynasty | null> {
    return await Dynasty.findById(dynastyId);
}

/**
 * Get dynasty with member info by ID
 */
export async function getDynastyWithMemberInfo(dynastyId: string): Promise<{ dynasty: IDynasty; members: IDynastyMember[] } | null> {
    const dynasty = await Dynasty.findById(dynastyId);
    if (!dynasty) return null;

    const members = await DynastyMember.find({ dynastyId }).sort({ role: 1, joinedAt: 1 });
    return { dynasty, members };
}

/**
 * Get dynasty by name in guild
 */
export async function getDynastyByName(name: string, guildId: string): Promise<IDynasty | null> {
    return await Dynasty.findOne({
        guildId,
        name: { $regex: new RegExp(`^${name}$`, 'i') }
    });
}

/**
 * Get user's dynasty membership
 */
export async function getUserDynastyMembership(discordId: string, guildId: string): Promise<IDynastyMember | null> {
    return await DynastyMember.findOne({ discordId, guildId });
}

/**
 * Get dynasty members
 */
export async function getDynastyMembers(dynastyId: string): Promise<IDynastyMember[]> {
    return await DynastyMember.find({ dynastyId }).sort({ role: 1, joinedAt: 1 });
}

/**
 * Calculate dynasty level based on weighted formula
 */
export function calculateDynastyLevel(
    totalWealth: number,
    activityScore: number,
    memberCount: number,
    ageInDays: number
): number {
    // Weighted formula: 40% wealth, 30% activity, 20% members, 10% age
    const wealthScore = Math.min(totalWealth / 10000, 100); // Cap at 100k PLC = 100 points
    const activityScoreNormalized = Math.min(activityScore / 100, 100); // Cap at 10k activity = 100 points
    const memberScore = Math.min(memberCount * 2, 100); // Cap at 50 members = 100 points
    const ageScore = Math.min(ageInDays / 10, 100); // Cap at 1000 days = 100 points

    const totalScore = (wealthScore * 0.4) + (activityScoreNormalized * 0.3) + (memberScore * 0.2) + (ageScore * 0.1);
    
    // Convert to level (1-10)
    return Math.min(Math.max(Math.floor(totalScore / 10) + 1, 1), 10);
}

/**
 * Update dynasty statistics
 */
export async function updateDynastyStats(dynastyId: string): Promise<void> {
    try {
        const dynasty = await Dynasty.findById(dynastyId);
        if (!dynasty) return;

        const members = await DynastyMember.find({ dynastyId });

        // Calculate total wealth from all members
        let totalWealth = dynasty.bankBalance;
        for (const member of members) {
            const user = await User.findOne({ discordId: member.discordId });
            if (user) {
                totalWealth += user.balance;
            }
        }

        // Calculate activity score (sum of all member milestone achievements)
        const activityScore = members.reduce((sum, member) => sum + member.milestonesAchieved, 0);

        // Calculate age in days
        const ageInDays = Math.floor((Date.now() - dynasty.createdAt.getTime()) / (1000 * 60 * 60 * 24));

        // Calculate new level
        const newLevel = calculateDynastyLevel(totalWealth, activityScore, members.length, ageInDays);

        // Update dynasty
        await Dynasty.findByIdAndUpdate(dynastyId, {
            totalWealth,
            memberCount: members.length,
            activityScore,
            level: newLevel,
            lastActivityAt: new Date()
        });

        console.log(`[Dynasty Service] Updated stats for dynasty ${dynastyId}: Level ${newLevel}, Wealth ${totalWealth}, Activity ${activityScore}`);
    } catch (error) {
        console.error('Error updating dynasty stats:', error);
    }
}

/**
 * Get dynasty leaderboard
 */
export async function getDynastyLeaderboard(
    guildId: string,
    sortBy: 'level' | 'wealth' | 'activity' | 'members' = 'level',
    limit: number = 10
): Promise<IDynasty[]> {
    try {
        let sortField: any = { level: -1, totalWealth: -1 };

        switch (sortBy) {
            case 'wealth':
                sortField = { totalWealth: -1, level: -1 };
                break;
            case 'activity':
                sortField = { activityScore: -1, level: -1 };
                break;
            case 'members':
                sortField = { memberCount: -1, level: -1 };
                break;
            default:
                sortField = { level: -1, totalWealth: -1 };
        }

        return await Dynasty.find({ guildId })
            .sort(sortField)
            .limit(limit);
    } catch (error) {
        console.error('Error getting dynasty leaderboard:', error);
        return [];
    }
}

/**
 * Get all dynasties in a guild
 */
export async function getAllDynasties(guildId: string): Promise<IDynasty[]> {
    return await Dynasty.find({ guildId }).sort({ level: -1, totalWealth: -1 });
}

/**
 * Send dynasty invitation
 */
export async function sendDynastyInvitation(
    dynastyId: string,
    inviterId: string,
    inviteeId: string,
    guildId: string,
    message?: string
): Promise<{ success: boolean; reason?: string; invite?: any }> {
    const session = await mongoose.startSession();

    try {
        return await session.withTransaction(async () => {
            // Check if inviter has permission
            const inviterMembership = await DynastyMember.findOne({ dynastyId, discordId: inviterId });
            if (!inviterMembership || !inviterMembership.permissions.canInvite) {
                return { success: false, reason: 'You do not have permission to invite members' };
            }

            // Check if invitee is already in a dynasty
            const existingMembership = await DynastyMember.findOne({ discordId: inviteeId, guildId });
            if (existingMembership) {
                return { success: false, reason: 'This user is already a member of a dynasty' };
            }

            // Check if there's already a pending invitation
            const existingInvite = await DynastyInvite.findOne({
                dynastyId,
                inviteeId,
                status: 'pending'
            });
            if (existingInvite) {
                return { success: false, reason: 'This user already has a pending invitation to your dynasty' };
            }

            // Check dynasty member limits
            const dynasty = await Dynasty.findById(dynastyId);
            if (!dynasty) {
                return { success: false, reason: 'Dynasty not found' };
            }

            if (dynasty.memberCount >= dynasty.maxMembers) {
                return { success: false, reason: 'Dynasty is at maximum member capacity' };
            }

            // Check pending invite limits
            if (dynasty.invitesPending >= DYNASTY_MAX_PENDING_INVITES) {
                return { success: false, reason: 'Dynasty has reached maximum pending invitations limit' };
            }

            // Create invitation
            const expiresAt = new Date(Date.now() + (DYNASTY_INVITE_EXPIRY_HOURS * 60 * 60 * 1000));
            const invite = await DynastyInvite.create([{
                dynastyId,
                guildId,
                inviterId,
                inviteeId,
                status: 'pending',
                message: message?.trim() || '',
                expiresAt
            }], { session });

            // Update dynasty pending invites count
            await Dynasty.findByIdAndUpdate(dynastyId, {
                $inc: { invitesPending: 1 }
            }, { session });

            console.log(`[Dynasty Service] Invitation sent from ${inviterId} to ${inviteeId} for dynasty ${dynastyId}`);

            return { success: true, invite: invite[0] };
        });
    } catch (error) {
        console.error('Error sending dynasty invitation:', error);
        return { success: false, reason: 'Failed to send invitation' };
    } finally {
        await session.endSession();
    }
}

/**
 * Accept dynasty invitation
 */
export async function acceptDynastyInvitation(
    inviteId: string,
    inviteeId: string,
    client: Client
): Promise<{ success: boolean; reason?: string; dynasty?: IDynasty; member?: IDynastyMember }> {
    const session = await mongoose.startSession();

    try {
        return await session.withTransaction(async () => {
            // Find and validate invitation
            const invite = await DynastyInvite.findById(inviteId);
            if (!invite) {
                return { success: false, reason: 'Invitation not found' };
            }

            if (invite.inviteeId !== inviteeId) {
                return { success: false, reason: 'This invitation is not for you' };
            }

            if (invite.status !== 'pending') {
                return { success: false, reason: 'This invitation is no longer valid' };
            }

            if (invite.expiresAt < new Date()) {
                // Mark as expired
                await DynastyInvite.findByIdAndUpdate(inviteId, { status: 'expired' }, { session });
                return { success: false, reason: 'This invitation has expired' };
            }

            // Check if user is already in a dynasty
            const existingMembership = await DynastyMember.findOne({
                discordId: inviteeId,
                guildId: invite.guildId
            });
            if (existingMembership) {
                return { success: false, reason: 'You are already a member of a dynasty' };
            }

            // Get dynasty and check capacity
            const dynasty = await Dynasty.findById(invite.dynastyId);
            if (!dynasty) {
                return { success: false, reason: 'Dynasty no longer exists' };
            }

            if (dynasty.memberCount >= dynasty.maxMembers) {
                return { success: false, reason: 'Dynasty is at maximum capacity' };
            }

            // Create membership
            const member = await DynastyMember.create([{
                dynastyId: invite.dynastyId,
                discordId: inviteeId,
                guildId: invite.guildId,
                role: 'member',
                joinedAt: new Date(),
                lastActiveAt: new Date(),
                contributionScore: 0,
                totalContributed: 0,
                milestonesAchieved: 0,
                invitedBy: invite.inviterId,
                permissions: {
                    canInvite: false,
                    canKick: false,
                    canWithdraw: false,
                    canManageBank: false
                }
            }], { session });

            // Update invitation status
            await DynastyInvite.findByIdAndUpdate(inviteId, {
                status: 'accepted',
                respondedAt: new Date()
            }, { session });

            // Update dynasty stats
            await Dynasty.findByIdAndUpdate(invite.dynastyId, {
                $inc: {
                    memberCount: 1,
                    invitesPending: -1
                },
                lastActivityAt: new Date()
            }, { session });

            console.log(`[Dynasty Service] User ${inviteeId} accepted invitation to dynasty ${invite.dynastyId}`);

            return { success: true, dynasty, member: member[0] };
        });
    } catch (error) {
        console.error('Error accepting dynasty invitation:', error);
        return { success: false, reason: 'Failed to accept invitation' };
    } finally {
        await session.endSession();
    }
}

/**
 * Decline dynasty invitation
 */
export async function declineDynastyInvitation(
    inviteId: string,
    inviteeId: string
): Promise<{ success: boolean; reason?: string }> {
    try {
        const invite = await DynastyInvite.findById(inviteId);
        if (!invite) {
            return { success: false, reason: 'Invitation not found' };
        }

        if (invite.inviteeId !== inviteeId) {
            return { success: false, reason: 'This invitation is not for you' };
        }

        if (invite.status !== 'pending') {
            return { success: false, reason: 'This invitation is no longer valid' };
        }

        // Update invitation status
        await DynastyInvite.findByIdAndUpdate(inviteId, {
            status: 'declined',
            respondedAt: new Date()
        });

        // Update dynasty pending invites count
        await Dynasty.findByIdAndUpdate(invite.dynastyId, {
            $inc: { invitesPending: -1 }
        });

        console.log(`[Dynasty Service] User ${inviteeId} declined invitation to dynasty ${invite.dynastyId}`);

        return { success: true };
    } catch (error) {
        console.error('Error declining dynasty invitation:', error);
        return { success: false, reason: 'Failed to decline invitation' };
    }
}

/**
 * Get pending invitations for a user
 */
export async function getUserPendingInvitations(discordId: string, guildId: string): Promise<any[]> {
    try {
        const invites = await DynastyInvite.find({
            inviteeId: discordId,
            guildId,
            status: 'pending',
            expiresAt: { $gt: new Date() }
        }).populate('dynastyId');

        return invites;
    } catch (error) {
        console.error('Error getting user pending invitations:', error);
        return [];
    }
}
