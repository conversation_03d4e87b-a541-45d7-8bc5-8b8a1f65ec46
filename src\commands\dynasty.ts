import {
    <PERSON>lashCommandBuilder,
    ChatInputCommandInteraction,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    EmbedBuilder
} from 'discord.js';
import { withError<PERSON>and<PERSON>, CommandError, DatabaseError } from '../utils/errorHandler';
import { createEconomyEmbed, createErrorEmbed, createSuccessEmbed, addUserInfo, formatCoins, EMOJIS } from '../utils/embedBuilder';
import {
    checkDynastyCreationEligibility,
    createDynasty,
    getDynastyByName,
    getDynastyById,
    getUserDynastyMembership,
    getDynastyMembers,
    updateDynastyStats,
    getDynastyLeaderboard,
    sendDynastyInvitation,
    DYNASTY_CREATION_COST,
    DYNASTY_MIN_BALANCE,
    DYNASTY_MIN_SERVER_DAYS
} from '../services/dynastyService';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('dynasty')
        .setDescription('Manage your Dynasty - exclusive groups for established players')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a new Dynasty')
                .addStringOption(option =>
                    option
                        .setName('name')
                        .setDescription('Dynasty name (3-20 characters)')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option
                        .setName('description')
                        .setDescription('Dynasty description (optional)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('info')
                .setDescription('View Dynasty information')
                .addStringOption(option =>
                    option
                        .setName('dynasty')
                        .setDescription('Dynasty name (leave empty for your own)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('leaderboard')
                .setDescription('View Dynasty leaderboard')
                .addStringOption(option =>
                    option
                        .setName('sort')
                        .setDescription('Sort by')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Level', value: 'level' },
                            { name: 'Total Wealth', value: 'wealth' },
                            { name: 'Activity Score', value: 'activity' },
                            { name: 'Member Count', value: 'members' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('invite')
                .setDescription('Invite a user to your Dynasty')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to invite')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option
                        .setName('message')
                        .setDescription('Optional invitation message')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('kick')
                .setDescription('Remove a member from your Dynasty')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to remove')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option
                        .setName('reason')
                        .setDescription('Reason for removal (optional)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('promote')
                .setDescription('Promote a member to Council')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to promote')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('demote')
                .setDescription('Demote a Council member to regular member')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to demote')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('transfer')
                .setDescription('Transfer Dynasty leadership to another member')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('New Dynasty leader')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Permanently delete your Dynasty')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('bank')
                .setDescription('Manage Dynasty treasury')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('leave')
                .setDescription('Leave your current Dynasty')
        ),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild?.id;
        const userId = interaction.user.id;

        if (!guildId) {
            throw new CommandError('This command can only be used in a server');
        }

        try {
            switch (subcommand) {
                case 'create':
                    await handleCreateDynasty(interaction);
                    break;
                case 'info':
                    await handleDynastyInfo(interaction);
                    break;
                case 'leaderboard':
                    await handleDynastyLeaderboard(interaction);
                    break;
                case 'invite':
                    await handleInviteUser(interaction);
                    break;
                case 'kick':
                    await handleKickUser(interaction);
                    break;
                case 'promote':
                    await handlePromoteUser(interaction);
                    break;
                case 'demote':
                    await handleDemoteUser(interaction);
                    break;
                case 'transfer':
                    await handleTransferLeadership(interaction);
                    break;
                case 'delete':
                    await handleDeleteDynasty(interaction);
                    break;
                case 'bank':
                    await handleDynastyBank(interaction);
                    break;
                case 'leave':
                    await handleLeaveDynasty(interaction);
                    break;
                default:
                    throw new CommandError('Unknown subcommand');
            }
        } catch (error) {
            console.error(`Error in dynasty ${subcommand}:`, error);
            if (error instanceof CommandError || error instanceof DatabaseError) {
                throw error;
            }
            throw new CommandError(`Failed to execute dynasty ${subcommand}`);
        }
    })
};

async function handleCreateDynasty(interaction: ChatInputCommandInteraction) {
    const name = interaction.options.getString('name', true);
    const description = interaction.options.getString('description') || '';
    const guildId = interaction.guild!.id;
    const userId = interaction.user.id;

    // Check eligibility
    const eligibility = await checkDynastyCreationEligibility(userId, guildId, interaction.client);
    if (!eligibility.eligible) {
        const embed = createErrorEmbed(
            'Dynasty Creation Failed',
            eligibility.reason || 'You are not eligible to create a dynasty'
        );
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }

    // Show confirmation dialog
    const confirmEmbed = createEconomyEmbed(
        `${EMOJIS.SUCCESS.CROWN} Create Dynasty: "${name}"`,
        [
            `**Cost:** ${formatCoins(DYNASTY_CREATION_COST)}`,
            `**Requirements Met:**`,
            `${EMOJIS.SUCCESS} Balance ≥ ${formatCoins(DYNASTY_MIN_BALANCE)}`,
            `${EMOJIS.SUCCESS} Server member ≥ ${DYNASTY_MIN_SERVER_DAYS} days`,
            `${EMOJIS.SUCCESS} Not in another Dynasty`,
            '',
            `**Description:** ${description || 'None'}`,
            '',
            '⚠️ **This action cannot be undone!**'
        ].join('\n')
    );

    const confirmButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
            .setCustomId(`dynasty_create_confirm_${userId}`)
            .setLabel('Create Dynasty')
            .setStyle(ButtonStyle.Success)
            .setEmoji(EMOJIS.SUCCESS.CROWN),
        new ButtonBuilder()
            .setCustomId(`dynasty_create_cancel_${userId}`)
            .setLabel('Cancel')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji(EMOJIS.CANCEL)
    );

    await interaction.reply({
        embeds: [confirmEmbed],
        components: [confirmButtons],
        ephemeral: true
    });

    // Store creation data for button handler
    (interaction.client as any).dynastyCreationData = (interaction.client as any).dynastyCreationData || new Map();
    (interaction.client as any).dynastyCreationData.set(userId, { name, description, guildId });
}

async function handleDynastyInfo(interaction: ChatInputCommandInteraction) {
    const dynastyName = interaction.options.getString('dynasty');
    const guildId = interaction.guild!.id;
    const userId = interaction.user.id;

    let dynasty;
    let userMembership;

    if (dynastyName) {
        // Look up specific dynasty
        dynasty = await getDynastyByName(dynastyName, guildId);
        if (!dynasty) {
            const embed = createErrorEmbed(
                'Dynasty Not Found',
                `No dynasty named "${dynastyName}" exists in this server.`
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }
    } else {
        // Show user's own dynasty
        userMembership = await getUserDynastyMembership(userId, guildId);
        if (!userMembership) {
            const embed = createErrorEmbed(
                'No Dynasty',
                'You are not a member of any dynasty. Use `/dynasty create` to start one!'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }
        dynasty = await getDynastyById(userMembership.dynastyId);
        if (!dynasty) {
            const embed = createErrorEmbed(
                'Dynasty Error',
                'Your dynasty could not be found. Please contact an administrator.'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }
    }

    // Get dynasty members
    const dynastyId = dynasty._id?.toString() || dynasty.id;
    const members = await getDynastyMembers(dynastyId);

    // Update stats before displaying
    await updateDynastyStats(dynastyId);

    // Refresh dynasty data
    const refreshedDynasty = await getDynastyById(dynastyId);
    if (refreshedDynasty) {
        dynasty = refreshedDynasty;
    }

    const embed = createEconomyEmbed(
        `${EMOJIS.SUCCESS.CROWN} Dynasty: ${dynasty.name}`,
        [
            `**Level:** ${dynasty.level}/10 ${getLevelEmoji(dynasty.level)}`,
            `**Members:** ${dynasty.memberCount}/${dynasty.maxMembers}`,
            `**Total Wealth:** ${formatCoins(dynasty.totalWealth)}`,
            `**Bank Balance:** ${formatCoins(dynasty.bankBalance)}`,
            `**Activity Score:** ${dynasty.activityScore}`,
            '',
            `**Description:** ${dynasty.description || 'No description set'}`,
            '',
            `**Founded:** <t:${Math.floor(dynasty.createdAt.getTime() / 1000)}:R>`,
            `**Last Active:** <t:${Math.floor(dynasty.lastActivityAt.getTime() / 1000)}:R>`
        ].join('\n')
    );

    // Add member list
    const founder = members.find(m => m.role === 'founder');
    const council = members.filter(m => m.role === 'council');
    const regularMembers = members.filter(m => m.role === 'member');

    let memberList = '';
    if (founder) {
        memberList += `${EMOJIS.SUCCESS.CROWN} **Founder:** <@${founder.discordId}>\n`;
    }
    if (council.length > 0) {
        memberList += `${EMOJIS.SUCCESS.STAR} **Council:** ${council.map(m => `<@${m.discordId}>`).join(', ')}\n`;
    }
    if (regularMembers.length > 0) {
        memberList += `${EMOJIS.MEMBER} **Members:** ${regularMembers.map(m => `<@${m.discordId}>`).join(', ')}\n`;
    }

    if (memberList) {
        embed.addFields({ name: 'Members', value: memberList, inline: false });
    }

    await interaction.reply({ embeds: [embed] });
}

function getLevelEmoji(level: number): string {
    const emojis = ['', '🥉', '🥈', '🥇', '💎', '👑', '⭐', '🌟', '✨', '🔥', '🏆'];
    return emojis[level] || '🏆';
}

async function handleDynastyLeaderboard(interaction: ChatInputCommandInteraction) {
    const sortBy = interaction.options.getString('sort') as 'level' | 'wealth' | 'activity' | 'members' || 'level';
    const guildId = interaction.guild!.id;

    try {
        const dynasties = await getDynastyLeaderboard(guildId, sortBy, 10);

        if (dynasties.length === 0) {
            const embed = createErrorEmbed(
                'No Dynasties Found',
                'No dynasties exist in this server yet. Use `/dynasty create` to start the first one!'
            );
            await interaction.reply({ embeds: [embed] });
            return;
        }

        const sortLabels = {
            level: 'Level',
            wealth: 'Total Wealth',
            activity: 'Activity Score',
            members: 'Member Count'
        };

        const embed = createEconomyEmbed(
            `${EMOJIS.SUCCESS.CROWN} Dynasty Leaderboard - ${sortLabels[sortBy]}`,
            ''
        );

        let description = '';
        for (let i = 0; i < dynasties.length; i++) {
            const dynasty = dynasties[i];
            const rank = i + 1;
            const rankEmoji = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;

            let sortValue = '';
            switch (sortBy) {
                case 'wealth':
                    sortValue = formatCoins(dynasty.totalWealth);
                    break;
                case 'activity':
                    sortValue = `${dynasty.activityScore} points`;
                    break;
                case 'members':
                    sortValue = `${dynasty.memberCount}/${dynasty.maxMembers} members`;
                    break;
                default:
                    sortValue = `Level ${dynasty.level}${getLevelEmoji(dynasty.level)}`;
            }

            description += `${rankEmoji} **${dynasty.name}** - ${sortValue}\n`;
        }

        embed.setDescription(description);

        // Add summary footer
        embed.setFooter({
            text: `Showing top ${dynasties.length} dynasties • Use /dynasty info [name] to view details`
        });

        await interaction.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Error in dynasty leaderboard:', error);
        const embed = createErrorEmbed(
            'Leaderboard Error',
            'Failed to load dynasty leaderboard. Please try again later.'
        );
        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

async function handleInviteUser(interaction: ChatInputCommandInteraction) {
    const targetUser = interaction.options.getUser('user', true);
    const message = interaction.options.getString('message');
    const guildId = interaction.guild!.id;
    const userId = interaction.user.id;

    // Check if user is trying to invite themselves
    if (targetUser.id === userId) {
        const embed = createErrorEmbed(
            'Invalid Invitation',
            'You cannot invite yourself to your own dynasty.'
        );
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }

    // Check if target is a bot
    if (targetUser.bot) {
        const embed = createErrorEmbed(
            'Invalid Invitation',
            'You cannot invite bots to dynasties.'
        );
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }

    try {
        // Get user's dynasty membership
        const userMembership = await getUserDynastyMembership(userId, guildId);
        if (!userMembership) {
            const embed = createErrorEmbed(
                'No Dynasty',
                'You must be a member of a dynasty to invite others. Use `/dynasty create` to start one!'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }

        // Check permissions
        if (!userMembership.permissions.canInvite) {
            const embed = createErrorEmbed(
                'Insufficient Permissions',
                'You do not have permission to invite members to this dynasty. Only the founder and council members can send invitations.'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }

        // Send invitation
        const result = await sendDynastyInvitation(
            userMembership.dynastyId,
            userId,
            targetUser.id,
            guildId,
            message || undefined
        );

        if (!result.success) {
            const embed = createErrorEmbed(
                'Invitation Failed',
                result.reason || 'Failed to send invitation'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }

        // Get dynasty info for the invitation
        const dynasty = await getDynastyById(userMembership.dynastyId);
        if (!dynasty) {
            const embed = createErrorEmbed(
                'Dynasty Error',
                'Dynasty information could not be retrieved.'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }

        // Create invitation embed
        const inviteEmbed = createEconomyEmbed(
            `${EMOJIS.SUCCESS.CROWN} Dynasty Invitation`,
            [
                `**${targetUser.username}**, you have been invited to join **${dynasty.name}**!`,
                '',
                `**Dynasty Level:** ${dynasty.level}/10 ${getLevelEmoji(dynasty.level)}`,
                `**Members:** ${dynasty.memberCount}/${dynasty.maxMembers}`,
                `**Total Wealth:** ${formatCoins(dynasty.totalWealth)}`,
                '',
                `**Invited by:** ${interaction.user.username}`,
                message ? `**Message:** ${message}` : '',
                '',
                `⏰ This invitation expires in 24 hours.`
            ].filter(line => line !== '').join('\n')
        );

        const inviteButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
            new ButtonBuilder()
                .setCustomId(`dynasty_invite_accept_${result.invite._id}`)
                .setLabel('Accept')
                .setStyle(ButtonStyle.Success)
                .setEmoji(EMOJIS.SUCCESS),
            new ButtonBuilder()
                .setCustomId(`dynasty_invite_decline_${result.invite._id}`)
                .setLabel('Decline')
                .setStyle(ButtonStyle.Danger)
                .setEmoji(EMOJIS.CANCEL)
        );

        // Send invitation to target user via DM
        try {
            await targetUser.send({
                embeds: [inviteEmbed],
                components: [inviteButtons]
            });

            // Confirm to inviter
            const confirmEmbed = createSuccessEmbed(
                `${EMOJIS.SUCCESS} Invitation Sent`,
                `Successfully sent dynasty invitation to **${targetUser.username}**. They will receive a direct message with the invitation details.`
            );
            await interaction.reply({ embeds: [confirmEmbed], ephemeral: true });
        } catch (dmError) {
            // If DM fails, send in channel with mention
            inviteEmbed.setDescription(
                `${targetUser}, you have been invited to join **${dynasty.name}**!\n\n` +
                inviteEmbed.data.description
            );

            await interaction.reply({
                content: `${targetUser}`,
                embeds: [inviteEmbed],
                components: [inviteButtons]
            });
        }
    } catch (error) {
        console.error('Error in dynasty invite:', error);
        const embed = createErrorEmbed(
            'Invitation Error',
            'Failed to send dynasty invitation. Please try again later.'
        );
        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

async function handleKickUser(interaction: ChatInputCommandInteraction) {
    await interaction.reply({ content: 'Dynasty kick system coming soon!', ephemeral: true });
}

async function handlePromoteUser(interaction: ChatInputCommandInteraction) {
    await interaction.reply({ content: 'Dynasty promotion system coming soon!', ephemeral: true });
}

async function handleDemoteUser(interaction: ChatInputCommandInteraction) {
    await interaction.reply({ content: 'Dynasty demotion system coming soon!', ephemeral: true });
}

async function handleTransferLeadership(interaction: ChatInputCommandInteraction) {
    await interaction.reply({ content: 'Dynasty leadership transfer coming soon!', ephemeral: true });
}

async function handleDeleteDynasty(interaction: ChatInputCommandInteraction) {
    await interaction.reply({ content: 'Dynasty deletion system coming soon!', ephemeral: true });
}

async function handleDynastyBank(interaction: ChatInputCommandInteraction) {
    await interaction.reply({ content: 'Dynasty bank system coming soon!', ephemeral: true });
}

async function handleLeaveDynasty(interaction: ChatInputCommandInteraction) {
    await interaction.reply({ content: 'Dynasty leave system coming soon!', ephemeral: true });
}
