import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction } from 'discord.js';
import { getLeaderboard } from '../services/economyService';
import { withErrorHandler, DatabaseError } from '../utils/errorHandler';
import { IUser } from '../models/User';
import { createEconomyEmbed, formatCoins, createQuickActionButtons, EMOJIS, COLORS } from '../utils/embedBuilder';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('leaderboard')
        .setDescription('Show the top users by balance')
        .addStringOption(option =>
            option
                .setName('filter')
                .setDescription('Filter leaderboard by dynasty')
                .setRequired(false)
                .addChoices(
                    { name: 'All Users', value: 'all' },
                    { name: 'Dynasty Members Only', value: 'dynasty' },
                    { name: 'Non-Dynasty Members', value: 'no_dynasty' }
                )
        ),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        try {
            const filter = interaction.options.getString('filter') || 'all';
            const guildId = interaction.guild?.id;
            const users = await getLeaderboard(10);

            if (users.length === 0) {
                const embed = createEconomyEmbed('Leaderboard')
                    .setDescription(`${EMOJIS.MISC.MAGNIFYING} No users found yet!\n\nBe the first to earn some coins and claim the top spot!`)
                    .setColor(COLORS.INFO);

                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }

            // Filter users to only include current guild members and build leaderboard entries
            const currentMemberEntries: string[] = [];
            let position = 1;

            for (const user of users) {
                try {
                    // Check if user is still a member of the guild
                    if (!interaction.guild) {
                        console.warn('[Leaderboard] No guild context available');
                        continue;
                    }

                    // Apply dynasty filter if specified
                    if (filter !== 'all' && guildId) {
                        try {
                            const { getUserDynastyMembership } = require('../services/dynastyService');
                            const membership = await getUserDynastyMembership(user.discordId, guildId);

                            if (filter === 'dynasty' && !membership) {
                                continue; // Skip non-dynasty members
                            } else if (filter === 'no_dynasty' && membership) {
                                continue; // Skip dynasty members
                            }
                        } catch (dynastyError) {
                            console.error('Error checking dynasty membership for leaderboard:', dynastyError);
                            // Continue without filtering if dynasty check fails
                        }
                    }

                    // First check cache, then fetch if needed
                    let guildMember;
                    try {
                        guildMember = interaction.guild.members.cache.get(user.discordId);
                        if (!guildMember) {
                            // Try to fetch the member if not in cache
                            guildMember = await interaction.guild.members.fetch(user.discordId);
                        }
                    } catch (fetchError) {
                        // User is not in the guild anymore, skip them
                        continue;
                    }

                    // If we get here, the user is still in the guild
                    // Try to get their display name
                    const displayName = guildMember.displayName || guildMember.user.username;

                    // Add special emojis for top positions
                    let positionEmoji = '';
                    if (position === 1) positionEmoji = '🥇';
                    else if (position === 2) positionEmoji = '🥈';
                    else if (position === 3) positionEmoji = '🥉';
                    else positionEmoji = `${EMOJIS.ROLES.MEDAL}`;

                    currentMemberEntries.push(`${positionEmoji} **#${position}** ${displayName} — ${formatCoins(user.balance)}`);
                    position++;

                    // Stop if we have enough entries for display
                    if (currentMemberEntries.length >= 10) {
                        break;
                    }
                } catch (error) {
                    // Log error but continue processing other users
                    console.error(`[Leaderboard] Error processing user ${user.discordId}:`, error);
                    continue;
                }
            }

            // Check if we have any current members to display
            if (currentMemberEntries.length === 0) {
                const embed = createEconomyEmbed('Leaderboard')
                    .setDescription(`${EMOJIS.MISC.MAGNIFYING} No current members found on the leaderboard!\n\nBe the first to earn some coins and claim the top spot!`)
                    .setColor(COLORS.INFO);

                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }

            // Create rich leaderboard embed
            const filterLabels = {
                'all': 'All Members',
                'dynasty': 'Dynasty Members',
                'no_dynasty': 'Non-Dynasty Members'
            };

            const title = filter === 'all' ? 'Phalanx Loyalty Coins Leaderboard' : `${filterLabels[filter as keyof typeof filterLabels]} Leaderboard`;

            const embed = createEconomyEmbed(title)
                .setDescription(
                    `${EMOJIS.SUCCESS.TROPHY} **Top ${currentMemberEntries.length} Current Members**\n\n` +
                    currentMemberEntries.join('\n') +
                    `\n\n${EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb higher!*`
                )
                .addFields({
                    name: `${EMOJIS.MISC.CALENDAR} Last Updated`,
                    value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
                    inline: true
                })
                .setFooter({
                    text: 'Use /balance to check your current position!'
                })
                .setThumbnail('https://cdn.discordapp.com/emojis/1234567890123456789.png'); // You can add a custom leaderboard icon

            // Create quick action buttons
            const actionButtons = createQuickActionButtons();

            await interaction.reply({
                embeds: [embed],
                components: [actionButtons],
                ephemeral: false
            });
        } catch (error: unknown) {
            if (error instanceof Error) {
                throw new DatabaseError(error.message);
            }
            throw new DatabaseError('Failed to fetch leaderboard data.');
        }
    })
};
