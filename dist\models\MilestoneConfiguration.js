"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const milestoneConfigurationSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    category: {
        type: String,
        enum: ['time_based', 'participation_diversity', 'loyalty', 'engagement'],
        required: [true, 'Category is required'],
        index: true
    },
    milestoneType: {
        type: String,
        required: [true, 'Milestone type is required'],
        index: true
    },
    enabled: {
        type: Boolean,
        default: true,
        index: true
    },
    rewardAmount: {
        type: Number,
        required: [true, 'Reward amount is required'],
        min: [1, 'Reward amount must be positive'],
        max: [100, 'Reward amount cannot exceed 100 PLC per milestone']
    },
    maxRewardsPerWeek: {
        type: Number,
        default: 10,
        min: [1, 'Must allow at least 1 reward per week'],
        max: [50, 'Cannot exceed 50 rewards per week']
    },
    maxRewardsPerDay: {
        type: Number,
        default: 3,
        min: [1, 'Must allow at least 1 reward per day'],
        max: [10, 'Cannot exceed 10 rewards per day']
    },
    diminishingReturns: {
        type: Boolean,
        default: true
    },
    diminishingFactor: {
        type: Number,
        default: 0.8,
        min: [0.1, 'Diminishing factor must be at least 0.1'],
        max: [1.0, 'Diminishing factor cannot exceed 1.0']
    },
    requirements: {
        threshold: {
            type: Number,
            min: [1, 'Threshold must be positive']
        },
        minMessageLength: {
            type: Number,
            default: 10,
            min: [1, 'Minimum message length must be positive']
        },
        minVoiceTime: {
            type: Number,
            default: 5,
            min: [1, 'Minimum voice time must be positive']
        },
        cooldownHours: {
            type: Number,
            default: 24,
            min: [1, 'Cooldown must be at least 1 hour'],
            max: [168, 'Cooldown cannot exceed 1 week']
        }
    }
}, {
    timestamps: true
});
// Compound indexes for efficient queries
milestoneConfigurationSchema.index({ guildId: 1, category: 1, enabled: 1 });
milestoneConfigurationSchema.index({ guildId: 1, milestoneType: 1 }, { unique: true });
exports.default = (0, mongoose_1.model)('MilestoneConfiguration', milestoneConfigurationSchema);
