"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const MilestoneConfiguration_1 = __importDefault(require("../models/MilestoneConfiguration"));
const UserActivity_1 = __importDefault(require("../models/UserActivity"));
const MilestoneAchievement_1 = __importDefault(require("../models/MilestoneAchievement"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const milestoneService_1 = require("../services/milestoneService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('milestones')
        .setDescription('View your milestone progress and achievements')
        .addSubcommand(subcommand => subcommand
        .setName('progress')
        .setDescription('View your current milestone progress and streaks'))
        .addSubcommand(subcommand => subcommand
        .setName('achievements')
        .setDescription('View your recent milestone achievements')
        .addIntegerOption(option => option
        .setName('limit')
        .setDescription('Number of recent achievements to show (1-20)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(20)))
        .addSubcommand(subcommand => subcommand
        .setName('available')
        .setDescription('View available milestones you can achieve'))
        .addSubcommand(subcommand => subcommand
        .setName('stats')
        .setDescription('View your overall milestone statistics')),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        const userId = interaction.user.id;
        switch (subcommand) {
            case 'progress':
                await handleProgress(interaction, guildId, userId);
                break;
            case 'achievements':
                await handleAchievements(interaction, guildId, userId);
                break;
            case 'available':
                await handleAvailable(interaction, guildId, userId);
                break;
            case 'stats':
                await handleStats(interaction, guildId, userId);
                break;
            default:
                throw new Error('Unknown subcommand');
        }
    })
};
async function handleProgress(interaction, guildId, userId) {
    const userActivity = await UserActivity_1.default.findOne({ discordId: userId, guildId });
    if (!userActivity) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('No Activity Data', 'You haven\'t been active enough to track milestone progress yet. Start participating in the server to begin earning milestones!');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const embed = (0, embedBuilder_1.createEconomyEmbed)(`${embedBuilder_1.EMOJIS.MILESTONE.PROGRESS} Your Milestone Progress`, 'Current activity streaks and progress towards milestones');
    (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
    // Login streak information
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.MILESTONE.STREAK} Login Streak`,
        value: `**Current Streak:** ${userActivity.loginStreak} days\n**Longest Streak:** ${userActivity.longestLoginStreak} days\n**Total Active Days:** ${userActivity.totalDaysActive} days`,
        inline: true
    });
    // Daily activity
    const dailyActivity = `**Messages:** ${userActivity.dailyMessageCount}\n**Voice Minutes:** ${userActivity.dailyVoiceMinutes}\n**Reactions:** ${userActivity.dailyReactionCount}\n**Channels Used:** ${userActivity.uniqueChannelsToday.length}`;
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.MILESTONE.MESSAGE} Today's Activity`,
        value: dailyActivity,
        inline: true
    });
    // Weekly activity
    const weeklyActivity = `**Messages:** ${userActivity.weeklyMessageCount}\n**Voice Minutes:** ${userActivity.weeklyVoiceMinutes}\n**Reactions:** ${userActivity.weeklyReactionCount}\n**Channels Used:** ${userActivity.uniqueChannelsThisWeek.length}`;
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.MILESTONE.DIVERSITY} This Week's Activity`,
        value: weeklyActivity,
        inline: true
    });
    // Server anniversary
    const daysSinceJoin = Math.floor((Date.now() - userActivity.serverJoinDate.getTime()) / (1000 * 60 * 60 * 24));
    const monthsInServer = Math.floor(daysSinceJoin / 30);
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.MILESTONE.ANNIVERSARY} Server Membership`,
        value: `**Days in Server:** ${daysSinceJoin}\n**Monthly Milestones:** ${monthsInServer}\n**Joined:** <t:${Math.floor(userActivity.serverJoinDate.getTime() / 1000)}:R>`,
        inline: false
    });
    await interaction.reply({ embeds: [embed] });
}
async function handleAchievements(interaction, guildId, userId) {
    const limit = interaction.options.getInteger('limit') || 10;
    const achievements = await MilestoneAchievement_1.default.find({ discordId: userId, guildId })
        .sort({ timestamp: -1 })
        .limit(limit);
    if (achievements.length === 0) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('No Achievements Yet', 'You haven\'t earned any milestone achievements yet. Keep being active in the server to start earning rewards!');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const embed = (0, embedBuilder_1.createEconomyEmbed)(`${embedBuilder_1.EMOJIS.MILESTONE.ACHIEVEMENT} Your Recent Achievements`, `Your last ${achievements.length} milestone achievements`);
    (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
    let achievementText = '';
    let totalRewards = 0;
    for (const achievement of achievements) {
        const emoji = getMilestoneEmoji(achievement.milestoneType);
        const timeAgo = `<t:${Math.floor(achievement.timestamp.getTime() / 1000)}:R>`;
        achievementText += `${emoji} **${achievement.details}**\n${(0, embedBuilder_1.formatCoins)(achievement.rewardAmount)} • ${timeAgo}\n\n`;
        totalRewards += achievement.rewardAmount;
    }
    embed.addFields({
        name: 'Recent Achievements',
        value: achievementText || 'No achievements found.',
        inline: false
    });
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Total Rewards Shown`,
        value: (0, embedBuilder_1.formatCoins)(totalRewards),
        inline: true
    });
    await interaction.reply({ embeds: [embed] });
}
async function handleAvailable(interaction, guildId, userId) {
    const configs = await MilestoneConfiguration_1.default.find({ guildId, enabled: true }).sort({ category: 1, milestoneType: 1 });
    if (configs.length === 0) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('No Milestones Available', 'No milestone configurations are currently enabled for this server. Contact an administrator to set up milestones.');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const embed = (0, embedBuilder_1.createEconomyEmbed)(`${embedBuilder_1.EMOJIS.MILESTONE.STAR} Available Milestones`, 'Milestones you can achieve through server activity');
    (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
    const categories = ['time_based', 'participation_diversity', 'loyalty', 'engagement'];
    for (const category of categories) {
        const categoryConfigs = configs.filter(c => c.category === category);
        if (categoryConfigs.length === 0)
            continue;
        const categoryName = category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        let fieldValue = '';
        for (const config of categoryConfigs) {
            const emoji = getMilestoneEmoji(config.milestoneType);
            const typeName = config.milestoneType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            const threshold = config.requirements.threshold || 'N/A';
            fieldValue += `${emoji} **${typeName}**\n${(0, embedBuilder_1.formatCoins)(config.rewardAmount)} • Threshold: ${threshold}\n\n`;
        }
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MILESTONE.MEDAL} ${categoryName}`,
            value: fieldValue,
            inline: false
        });
    }
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Tips`,
        value: `• Milestones are automatically awarded when you meet the requirements\n• Some milestones have daily/weekly limits\n• Repeated achievements may have diminishing returns\n• Use \`/milestones progress\` to track your current activity`,
        inline: false
    });
    await interaction.reply({ embeds: [embed] });
}
async function handleStats(interaction, guildId, userId) {
    try {
        const stats = await (0, milestoneService_1.getUserMilestoneStats)(userId, guildId);
        const embed = (0, embedBuilder_1.createEconomyEmbed)(`${embedBuilder_1.EMOJIS.MILESTONE.TROPHY} Your Milestone Statistics`, 'Overall milestone achievement statistics');
        (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MILESTONE.ACHIEVEMENT} Total Achievements`,
            value: `**${stats.totalAchievements}** milestones earned`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Total Rewards`,
            value: (0, embedBuilder_1.formatCoins)(stats.totalRewards),
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.MILESTONE.STREAK} Current Streaks`,
            value: `**Login Streak:** ${stats.currentStreaks.loginStreak} days\n**Best Streak:** ${stats.currentStreaks.longestLoginStreak} days`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.MILESTONE.PROGRESS} This Week`,
            value: `**Achievements:** ${stats.weeklyProgress.achievementsThisWeek}/${stats.weeklyProgress.maxWeeklyAchievements}\n**Progress:** ${Math.round((stats.weeklyProgress.achievementsThisWeek / stats.weeklyProgress.maxWeeklyAchievements) * 100)}%`,
            inline: true
        });
        if (stats.recentAchievements.length > 0) {
            const lastAchievement = stats.recentAchievements[0];
            const timeAgo = `<t:${Math.floor(lastAchievement.timestamp.getTime() / 1000)}:R>`;
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MILESTONE.STAR} Latest Achievement`,
                value: `${lastAchievement.details}\n${(0, embedBuilder_1.formatCoins)(lastAchievement.rewardAmount)} • ${timeAgo}`,
                inline: false
            });
        }
        await interaction.reply({ embeds: [embed] });
    }
    catch (error) {
        console.error('[Milestones Command] Error getting user stats:', error);
        throw new errorHandler_1.DatabaseError('Failed to retrieve milestone statistics');
    }
}
function getMilestoneEmoji(milestoneType) {
    const emojiMap = {
        'login_streak': embedBuilder_1.EMOJIS.MILESTONE.STREAK,
        'channel_diversity_daily': embedBuilder_1.EMOJIS.MILESTONE.DIVERSITY,
        'channel_diversity_weekly': embedBuilder_1.EMOJIS.MILESTONE.DIVERSITY,
        'voice_diversity_daily': embedBuilder_1.EMOJIS.MILESTONE.VOICE,
        'voice_diversity_weekly': embedBuilder_1.EMOJIS.MILESTONE.VOICE,
        'reaction_diversity_daily': '😄',
        'reaction_diversity_weekly': '😄',
        'server_anniversary': embedBuilder_1.EMOJIS.MILESTONE.ANNIVERSARY,
        'total_activity_milestone': embedBuilder_1.EMOJIS.MILESTONE.PROGRESS,
        'voice_time_daily': embedBuilder_1.EMOJIS.MILESTONE.VOICE,
        'voice_time_weekly': embedBuilder_1.EMOJIS.MILESTONE.VOICE,
        'message_count_daily': embedBuilder_1.EMOJIS.MILESTONE.MESSAGE,
        'message_count_weekly': embedBuilder_1.EMOJIS.MILESTONE.MESSAGE
    };
    return emojiMap[milestoneType] || embedBuilder_1.EMOJIS.MILESTONE.STAR;
}
