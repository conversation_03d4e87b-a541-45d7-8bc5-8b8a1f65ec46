import {
    SlashCommandBuilder,
    ChatInputCommandInteraction,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle
} from 'discord.js';
import { withError<PERSON>and<PERSON>, CommandError } from '../utils/errorHandler';
import { getIncomeGuideText } from '../services/incomeGuideService';

// Command definitions with categories
const commands = [
    // Basic commands
    { name: 'balance', label: 'Balance', description: 'Check your Phalanx Loyalty Coin balance', category: 'basic' },
    { name: 'pay', label: 'Pay', description: 'Pay another user Phalanx Loyalty Coins', category: 'basic' },
    { name: 'history', label: 'History', description: 'Show your recent transaction history', category: 'basic' },
    { name: 'leaderboard', label: 'Leaderboard', description: 'Show the top users by balance', category: 'basic' },

    // Role commands
    { name: 'roles', label: 'Roles', description: 'Display all role achievements and your progress', category: 'roles' },

    // Dynasty commands
    { name: 'dynasty', label: 'Dynasty', description: 'Manage your Dynasty - exclusive groups for established players', category: 'dynasty' },

    // Admin commands
    { name: 'addrole', label: 'Add Role', description: 'Add a role achievement (admin only)', category: 'admin' },
    { name: 'editrole', label: 'Edit Role', description: 'Edit a role achievement (admin only)', category: 'admin' },
    { name: 'removerole', label: 'Remove Role', description: 'Remove a role achievement (admin only)', category: 'admin' },
    { name: 'give', label: 'Give', description: 'Give coins to a user (admin only)', category: 'admin' },
    { name: 'fine', label: 'Fine', description: 'Fine (remove coins from) a user (admin only)', category: 'admin' },
    { name: 'tax', label: 'Tax', description: 'Configure automatic taxation system (admin only)', category: 'admin' },
    { name: 'starterbalance', label: 'Starter Balance', description: 'Manage starter balance rules for roles (admin only)', category: 'admin' },
    { name: 'automessage', label: 'Auto Message', description: 'Configure automated messages for server events (admin only)', category: 'admin' },
    { name: 'editautomessage', label: 'Edit Auto Message', description: 'Edit existing automated messages (admin only)', category: 'admin' },
    { name: 'richestrole', label: 'Richest Role', description: 'Assign role to user with highest PLC balance (admin only)', category: 'admin' },
    { name: 'incomecredentials', label: 'Income Guide', description: 'Customize income earning guide text in /help command (admin only)', category: 'admin' }
];

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Show help and command buttons'),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        try {
            // Get custom income guide text for this guild
            const guildId = interaction.guild?.id;
            const incomeGuideText = guildId ? await getIncomeGuideText(guildId) :
                '💰 **Ways to Earn Phalanx Loyalty Coins**\n⏰ **Playing Time**\n• 10 hours/week = 150 Coins\n🔨 **Material Contributions**\n• Stack of Iron Ingots = 20 Coins\n• Stack of Gold = 10 Coins\n• Stack of Diamonds = 100 Coins\n🏰 **Territory Capturing**\n• Capture 1 node = 100 Coins\n• Help conquer a town = 300 Coins\n⚔️ **War Achievements**\n• Each kill during war = 15 Coins\n🤝 **Recruitment**\n• Active invite for 1 week = 500 Coins\n📝 **Important Note**\nTo claim your earnings, you must open a ticket in #tickets channel!';

            // Create buttons for each category
            const basicButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
                commands
                    .filter(cmd => cmd.category === 'basic')
                    .map(cmd =>
                        new ButtonBuilder()
                            .setCustomId(`help_${cmd.name}`)
                            .setLabel(cmd.label)
                            .setStyle(ButtonStyle.Primary)
                    )
            );

            const roleButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
                commands
                    .filter(cmd => cmd.category === 'roles')
                    .map(cmd =>
                        new ButtonBuilder()
                            .setCustomId(`help_${cmd.name}`)
                            .setLabel(cmd.label)
                            .setStyle(ButtonStyle.Success)
                    )
            );

            const dynastyButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
                commands
                    .filter(cmd => cmd.category === 'dynasty')
                    .map(cmd =>
                        new ButtonBuilder()
                            .setCustomId(`help_${cmd.name}`)
                            .setLabel(cmd.label)
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('👑')
                    )
            );

            // Split admin commands into multiple rows since Discord allows max 5 buttons per row
            const adminCommands = commands.filter(cmd => cmd.category === 'admin');
            const adminButtons1 = new ActionRowBuilder<ButtonBuilder>().addComponents(
                adminCommands.slice(0, 5).map(cmd =>
                    new ButtonBuilder()
                        .setCustomId(`help_${cmd.name}`)
                        .setLabel(cmd.label)
                        .setStyle(ButtonStyle.Danger)
                )
            );

            const adminButtons2 = new ActionRowBuilder<ButtonBuilder>().addComponents(
                adminCommands.slice(5, 10).map(cmd =>
                    new ButtonBuilder()
                        .setCustomId(`help_${cmd.name}`)
                        .setLabel(cmd.label)
                        .setStyle(ButtonStyle.Danger)
                )
            );

            const content = [
                '**Available Commands:**',
                '',
                '🔹 __Basic Commands:__',
                ...commands
                    .filter(cmd => cmd.category === 'basic')
                    .map(cmd => `• **/${cmd.name}**: ${cmd.description}`),
                '',
                '🔸 __Role Commands:__',
                ...commands
                    .filter(cmd => cmd.category === 'roles')
                    .map(cmd => `• **/${cmd.name}**: ${cmd.description}`),
                '',
                '👑 __Dynasty Commands:__',
                ...commands
                    .filter(cmd => cmd.category === 'dynasty')
                    .map(cmd => `• **/${cmd.name}**: ${cmd.description}`),
                '',
                '⚠️ __Admin Commands:__',
                ...commands
                    .filter(cmd => cmd.category === 'admin')
                    .map(cmd => `• **/${cmd.name}**: ${cmd.description}`),
                '',
                incomeGuideText
            ].join('\n');

            // Create the "Claim Your Coins!" link button
            const claimButton = new ActionRowBuilder<ButtonBuilder>().addComponents(
                new ButtonBuilder()
                    .setLabel('Claim Your Coins!')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.gg/PRd5rqYA6w')
            );

            await interaction.reply({
                content,
                components: [basicButtons, roleButtons, dynastyButtons, adminButtons1, adminButtons2, claimButton],
                ephemeral: false // Changed to false so buttons work properly
            });
        } catch (error: unknown) {
            if (error instanceof Error) {
                throw new CommandError(error.message);
            }
            throw new CommandError('An error occurred while showing help.');
        }
    })
};
