import { Schema, model, Document } from 'mongoose';

export interface IDynastyTransaction extends Document {
    dynastyId: string;
    guildId: string;
    discordId: string; // Who performed the transaction
    type: 'deposit' | 'withdrawal' | 'tax_collection' | 'milestone_bonus' | 'creation_fee' | 'transfer_in' | 'transfer_out';
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    details?: string;
    approvedBy?: string; // Discord ID of who approved (for withdrawals requiring approval)
    relatedTransactionId?: string; // For linking related transactions
    timestamp: Date;
}

const dynastyTransactionSchema = new Schema<IDynastyTransaction>({
    dynastyId: {
        type: String,
        required: [true, 'Dynasty ID is required'],
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        index: true
    },
    type: {
        type: String,
        enum: ['deposit', 'withdrawal', 'tax_collection', 'milestone_bonus', 'creation_fee', 'transfer_in', 'transfer_out'],
        required: [true, 'Transaction type is required'],
        index: true
    },
    amount: {
        type: Number,
        required: [true, 'Amount is required'],
        validate: {
            validator: function(v: number): boolean {
                return v !== 0;
            },
            message: 'Amount cannot be zero'
        }
    },
    balanceBefore: {
        type: Number,
        required: [true, 'Balance before is required'],
        min: [0, 'Balance before cannot be negative']
    },
    balanceAfter: {
        type: Number,
        required: [true, 'Balance after is required'],
        min: [0, 'Balance after cannot be negative']
    },
    details: {
        type: String,
        maxlength: [500, 'Details cannot exceed 500 characters']
    },
    approvedBy: {
        type: String,
        index: true
    },
    relatedTransactionId: {
        type: String,
        index: true
    },
    timestamp: {
        type: Date,
        default: Date.now,
        index: true
    }
}, {
    timestamps: false // Using custom timestamp field
});

// Compound indexes for efficient queries
dynastyTransactionSchema.index({ dynastyId: 1, timestamp: -1 });
dynastyTransactionSchema.index({ guildId: 1, timestamp: -1 });
dynastyTransactionSchema.index({ discordId: 1, timestamp: -1 });
dynastyTransactionSchema.index({ dynastyId: 1, type: 1, timestamp: -1 });

export default model<IDynastyTransaction>('DynastyTransaction', dynastyTransactionSchema);
