"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const node_cron_1 = __importDefault(require("node-cron"));
const reactionRewardsService_1 = require("./services/reactionRewardsService");
const taxService_1 = require("./services/taxService");
const starterBalanceService_1 = require("./services/starterBalanceService");
const automessageService_1 = require("./services/automessageService");
const userCleanupService_1 = require("./services/userCleanupService");
const milestoneService_1 = require("./services/milestoneService");
const embedBuilder_1 = require("./utils/embedBuilder");
const errorHandler_1 = require("./utils/errorHandler");
const memoryManager_1 = __importDefault(require("./services/memoryManager"));
dotenv_1.default.config();
mongoose_1.default.connect(process.env.MONGODB_URI)
    .then(async () => {
    console.log('Connected to MongoDB');
    // Database cleanup and initialization
    await initializeDatabase();
})
    .catch((err) => console.error('MongoDB connection error:', err));
async function initializeDatabase() {
    try {
        console.log('Initializing database...');
        // Get the users collection
        const db = mongoose_1.default.connection.db;
        if (!db) {
            throw new Error('Database connection not established');
        }
        const usersCollection = db.collection('users');
        // Check for existing indexes
        const indexes = await usersCollection.indexes();
        console.log('Existing indexes:', indexes.map(idx => idx.name));
        // Remove old userId index if it exists
        try {
            await usersCollection.dropIndex('userId_1');
            console.log('Dropped old userId_1 index');
        }
        catch (error) {
            // Index might not exist, which is fine
            console.log('userId_1 index not found (this is expected)');
        }
        // Clean up any records with null discordId
        const deleteResult = await usersCollection.deleteMany({
            $or: [
                { discordId: null },
                { discordId: { $exists: false } },
                { userId: { $exists: true } } // Remove old schema records
            ]
        });
        if (deleteResult.deletedCount > 0) {
            console.log(`Cleaned up ${deleteResult.deletedCount} corrupted user records`);
        }
        // Ensure proper index on discordId
        await usersCollection.createIndex({ discordId: 1 }, { unique: true });
        console.log('Ensured discordId index exists');
        console.log('Database initialization complete');
    }
    catch (error) {
        console.error('Database initialization error:', error);
    }
}
const client = new discord_js_1.Client({
    intents: [
        discord_js_1.GatewayIntentBits.Guilds,
        discord_js_1.GatewayIntentBits.GuildMessages,
        discord_js_1.GatewayIntentBits.MessageContent,
        discord_js_1.GatewayIntentBits.GuildMembers,
        discord_js_1.GatewayIntentBits.GuildMessageReactions
    ]
});
client.commands = new discord_js_1.Collection();
// Initialize memory manager
const memoryManager = memoryManager_1.default.getInstance();
const commandsPath = path_1.default.join(__dirname, 'commands');
const commandFiles = fs_1.default.readdirSync(commandsPath).filter(file => file.endsWith('.js') || file.endsWith('.ts'));
for (const file of commandFiles) {
    const filePath = path_1.default.join(commandsPath, file);
    const command = require(filePath);
    if (command.data && command.execute) {
        client.commands.set(command.data.name, command);
    }
}
client.once('ready', () => {
    console.log(`Logged in as ${client.user?.tag}`);
    // Initialize tax collection cron job (runs every hour)
    node_cron_1.default.schedule('0 * * * *', async () => {
        console.log('[Tax Collection] Running scheduled tax collection check...');
        try {
            // Process tax collection for all guilds the bot is in
            for (const [guildId, guild] of client.guilds.cache) {
                try {
                    const result = await (0, taxService_1.processTaxCollection)(client, guildId);
                    if (result.totalProcessed > 0) {
                        console.log(`[Tax Collection] Guild ${guild.name}: Processed ${result.totalProcessed}, Taxed ${result.totalTaxed}, Roles Removed ${result.totalRolesRemoved}`);
                        if (result.errors.length > 0) {
                            console.error(`[Tax Collection] Guild ${guild.name} errors:`, result.errors);
                        }
                    }
                }
                catch (error) {
                    console.error(`[Tax Collection] Failed for guild ${guild.name}:`, error);
                }
            }
        }
        catch (error) {
            console.error('[Tax Collection] Cron job error:', error);
        }
    }, {
        timezone: "UTC"
    });
    console.log('[Tax Collection] Cron job initialized - running every hour');
    // Initialize milestone tracking cron job (runs daily at midnight UTC)
    node_cron_1.default.schedule('0 0 * * *', async () => {
        console.log('[Milestone Tracking] Running daily milestone check...');
        try {
            // Process login streak tracking for all guilds
            for (const [guildId, guild] of client.guilds.cache) {
                try {
                    // This will be handled by the milestone service when users are active
                    // The cron job mainly serves as a daily reset trigger
                    console.log(`[Milestone Tracking] Daily reset processed for guild ${guild.name}`);
                }
                catch (error) {
                    console.error(`[Milestone Tracking] Failed for guild ${guild.name}:`, error);
                }
            }
        }
        catch (error) {
            console.error('[Milestone Tracking] Cron job error:', error);
        }
    }, {
        timezone: "UTC"
    });
    console.log('[Milestone Tracking] Daily cron job initialized - running at midnight UTC');
});
client.on('interactionCreate', async (interaction) => {
    if (interaction.isChatInputCommand()) {
        const command = client.commands.get(interaction.commandName);
        if (!command)
            return;
        try {
            await command.execute(interaction);
        }
        catch (error) {
            await (0, errorHandler_1.handleCommandError)(interaction, error);
        }
    }
    else if (interaction.isButton()) {
        // Handle button interactions
        try {
            const { customId } = interaction;
            if (customId === 'quick_balance') {
                const balanceCommand = client.commands.get('balance');
                if (balanceCommand) {
                    await balanceCommand.execute(interaction);
                }
            }
            else if (customId === 'quick_leaderboard') {
                const leaderboardCommand = client.commands.get('leaderboard');
                if (leaderboardCommand) {
                    await leaderboardCommand.execute(interaction);
                }
            }
            else if (customId === 'quick_roles') {
                const rolesCommand = client.commands.get('roles');
                if (rolesCommand) {
                    await rolesCommand.execute(interaction);
                }
            }
            else if (customId.startsWith('buy_role_')) {
                // Handle role achievement info buttons
                const roleId = customId.replace('buy_role_', '');
                await interaction.reply({
                    content: `Role achievements are automatically unlocked when you reach the required PLC balance! Keep earning coins to unlock this achievement.`,
                    ephemeral: true
                });
            }
            else if (customId === 'announce_confirm') {
                // Handle announcement confirmation
                const pendingAnnouncements = global.pendingAnnouncements;
                if (!pendingAnnouncements) {
                    await interaction.reply({
                        content: 'No pending announcements found. Please try the command again.',
                        ephemeral: true
                    });
                    return;
                }
                const originalInteractionId = interaction.message?.interaction?.id;
                const announcementData = pendingAnnouncements.get(originalInteractionId);
                if (!announcementData) {
                    await interaction.reply({
                        content: 'Announcement data not found or expired. Please try the command again.',
                        ephemeral: true
                    });
                    return;
                }
                // Clean up the pending announcement
                pendingAnnouncements.delete(originalInteractionId);
                // Process the announcement
                await interaction.deferUpdate();
                const announceModule = require('./commands/announce');
                await announceModule.processAnnouncement(interaction, announcementData);
            }
            else if (customId === 'announce_cancel') {
                // Handle announcement cancellation
                const pendingAnnouncements = global.pendingAnnouncements;
                const originalInteractionId = interaction.message?.interaction?.id;
                if (pendingAnnouncements && originalInteractionId) {
                    pendingAnnouncements.delete(originalInteractionId);
                }
                await interaction.update({
                    content: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Announcement cancelled.`,
                    embeds: [],
                    components: []
                });
            }
            else if (customId.startsWith('help_')) {
                // Handle help command buttons
                const commandName = customId.replace('help_', '');
                // Commands that can be executed directly
                const instantTriggerCommands = ['balance', 'roles', 'leaderboard', 'history'];
                if (instantTriggerCommands.includes(commandName)) {
                    // Execute the command directly
                    const command = client.commands.get(commandName);
                    if (command) {
                        await command.execute(interaction);
                    }
                    else {
                        await interaction.reply({
                            content: `Command "${commandName}" not found.`,
                            ephemeral: true
                        });
                    }
                }
                else {
                    // For other commands, show usage information
                    const commandDescriptions = {
                        'pay': 'Transfer coins to another user. Usage: `/pay @user amount`',
                        'addrole': '**[Admin Only]** Add a role achievement. Usage: `/addrole @role price`',
                        'editrole': '**[Admin Only]** Edit a role achievement. Usage: `/editrole role_name new_price`',
                        'removerole': '**[Admin Only]** Remove a role achievement. Usage: `/removerole role_name`',
                        'give': '**[Admin Only]** Give coins to a user. Usage: `/give @user amount`',
                        'fine': '**[Admin Only]** Remove coins from a user. Usage: `/fine @user amount`',
                        'tax': '**[Admin Only]** Configure automatic taxation system. Usage: `/tax status:on/off frequency:weeks amount:plc role:@role`',
                        'starterbalance': '**[Admin Only]** Manage starter balance rules. Usage: `/starterbalance action:add/edit/remove/list role:@role amount:plc`',
                        'incomecredentials': '**[Admin Only]** Customize income earning guide text displayed in /help command. Supports line breaks (\\n) and formatting. Usage: `/incomecredentials text:"Your custom income guide text"`',
                        'automessage': '**[Admin Only]** Manage automated messages for server events. Usage: `/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!`',
                        'placeholders': 'View available placeholders for automated messages. Usage: `/placeholders`',
                        'testcleanup': '**[Admin Only]** Test user data cleanup functionality. Usage: `/testcleanup user:@user action:check/simulate`'
                    };
                    const description = commandDescriptions[commandName] || `Use the /${commandName} command.`;
                    await interaction.reply({
                        content: `**/${commandName}**\n${description}`,
                        ephemeral: true
                    });
                }
            }
            else if (customId.startsWith('dynasty_create_confirm_')) {
                // Handle dynasty creation confirmation
                const userId = customId.replace('dynasty_create_confirm_', '');
                if (interaction.user.id !== userId) {
                    await interaction.reply({
                        content: 'You cannot confirm someone else\'s dynasty creation.',
                        ephemeral: true
                    });
                    return;
                }
                const dynastyCreationData = interaction.client.dynastyCreationData?.get(userId);
                if (!dynastyCreationData) {
                    await interaction.reply({
                        content: 'Dynasty creation data not found or expired. Please try the command again.',
                        ephemeral: true
                    });
                    return;
                }
                await interaction.deferUpdate();
                try {
                    const { createDynasty } = require('./services/dynastyService');
                    const { createSuccessEmbed, formatCoins, EMOJIS } = require('./utils/embedBuilder');
                    const result = await createDynasty(userId, dynastyCreationData.guildId, dynastyCreationData.name, dynastyCreationData.description, interaction.client);
                    const successEmbed = createSuccessEmbed(`${EMOJIS.CROWN} Dynasty Created!`, [
                        `**Dynasty Name:** ${result.dynasty.name}`,
                        `**Founder:** <@${userId}>`,
                        `**Creation Cost:** ${formatCoins(1000)} (deducted)`,
                        '',
                        `Welcome to your new Dynasty! Use \`/dynasty info\` to view details and \`/dynasty invite\` to recruit members.`
                    ].join('\n'));
                    await interaction.editReply({
                        embeds: [successEmbed],
                        components: []
                    });
                    // Clean up creation data
                    interaction.client.dynastyCreationData?.delete(userId);
                }
                catch (error) {
                    const { createErrorEmbed } = require('./utils/embedBuilder');
                    const errorEmbed = createErrorEmbed('Dynasty Creation Failed', error instanceof Error ? error.message : 'An unexpected error occurred');
                    await interaction.editReply({
                        embeds: [errorEmbed],
                        components: []
                    });
                }
            }
            else if (customId.startsWith('dynasty_create_cancel_')) {
                // Handle dynasty creation cancellation
                const userId = customId.replace('dynasty_create_cancel_', '');
                if (interaction.user.id !== userId) {
                    await interaction.reply({
                        content: 'You cannot cancel someone else\'s dynasty creation.',
                        ephemeral: true
                    });
                    return;
                }
                const { createErrorEmbed, EMOJIS } = require('./utils/embedBuilder');
                const cancelEmbed = createErrorEmbed(`${EMOJIS.CANCEL} Dynasty Creation Cancelled`, 'Dynasty creation has been cancelled. No coins were deducted.');
                await interaction.update({
                    embeds: [cancelEmbed],
                    components: []
                });
                // Clean up creation data
                interaction.client.dynastyCreationData?.delete(userId);
            }
            else if (customId.startsWith('dynasty_invite_accept_')) {
                // Handle dynasty invitation acceptance
                const inviteId = customId.replace('dynasty_invite_accept_', '');
                const userId = interaction.user.id;
                try {
                    const { acceptDynastyInvitation } = require('./services/dynastyService');
                    const { createSuccessEmbed, createErrorEmbed, formatCoins, EMOJIS } = require('./utils/embedBuilder');
                    const result = await acceptDynastyInvitation(inviteId, userId, interaction.client);
                    if (!result.success) {
                        const errorEmbed = createErrorEmbed('Invitation Error', result.reason || 'Failed to accept invitation');
                        await interaction.update({
                            embeds: [errorEmbed],
                            components: []
                        });
                        return;
                    }
                    const successEmbed = createSuccessEmbed(`${EMOJIS.CROWN} Welcome to ${result.dynasty.name}!`, [
                        `You have successfully joined **${result.dynasty.name}**!`,
                        '',
                        `**Dynasty Level:** ${result.dynasty.level}/10`,
                        `**Members:** ${result.dynasty.memberCount}/${result.dynasty.maxMembers}`,
                        '',
                        `Use \`/dynasty info\` to view dynasty details and \`/dynasty bank\` to contribute to the treasury.`
                    ].join('\n'));
                    await interaction.update({
                        embeds: [successEmbed],
                        components: []
                    });
                }
                catch (error) {
                    const { createErrorEmbed } = require('./utils/embedBuilder');
                    const errorEmbed = createErrorEmbed('Invitation Error', 'An unexpected error occurred while accepting the invitation');
                    await interaction.update({
                        embeds: [errorEmbed],
                        components: []
                    });
                }
            }
            else if (customId.startsWith('dynasty_invite_decline_')) {
                // Handle dynasty invitation decline
                const inviteId = customId.replace('dynasty_invite_decline_', '');
                const userId = interaction.user.id;
                try {
                    const { declineDynastyInvitation } = require('./services/dynastyService');
                    const { createErrorEmbed, EMOJIS } = require('./utils/embedBuilder');
                    const result = await declineDynastyInvitation(inviteId, userId);
                    if (!result.success) {
                        const errorEmbed = createErrorEmbed('Invitation Error', result.reason || 'Failed to decline invitation');
                        await interaction.update({
                            embeds: [errorEmbed],
                            components: []
                        });
                        return;
                    }
                    const declineEmbed = createErrorEmbed(`${EMOJIS.CANCEL} Invitation Declined`, 'You have declined the dynasty invitation.');
                    await interaction.update({
                        embeds: [declineEmbed],
                        components: []
                    });
                }
                catch (error) {
                    const { createErrorEmbed } = require('./utils/embedBuilder');
                    const errorEmbed = createErrorEmbed('Invitation Error', 'An unexpected error occurred while declining the invitation');
                    await interaction.update({
                        embeds: [errorEmbed],
                        components: []
                    });
                }
            }
            else {
                await interaction.reply({
                    content: 'This button interaction is not yet implemented.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            await (0, errorHandler_1.handleButtonError)(interaction, error);
        }
    }
});
// Message handler for mention reactions and milestone tracking
client.on('messageCreate', async (message) => {
    try {
        // Ignore bot messages and DMs
        if (message.author.bot || !message.guild)
            return;
        // Check if bot is mentioned
        const botMentioned = message.mentions.has(client.user);
        if (botMentioned) {
            // React with checkmark emoji
            await message.react('✅');
        }
        // Track message activity for milestones
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(client, message.author.id, message.guild.id, 'message', {
                channelId: message.channel.id,
                messageContent: message.content,
                messageLength: message.content.length,
                timestamp: message.createdAt
            });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] User ${message.author.tag} achieved ${milestoneResults.length} milestone(s) from message activity`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing message milestones:', error);
        }
    }
    catch (error) {
        console.error('[Message Handler] Error in messageCreate handler:', error);
    }
});
// Reaction rewards and milestone tracking event handler
client.on('messageReactionAdd', async (reaction, user) => {
    try {
        // Handle partial reactions and users
        if (reaction.partial) {
            try {
                await reaction.fetch();
            }
            catch (error) {
                console.error('[Reaction Handler] Failed to fetch reaction:', error);
                return;
            }
        }
        if (user.partial) {
            try {
                await user.fetch();
            }
            catch (error) {
                console.error('[Reaction Handler] Failed to fetch user:', error);
                return;
            }
        }
        // Skip bot users
        if (user.bot)
            return;
        const fullReaction = reaction;
        const fullUser = user;
        // Process the reaction reward
        await (0, reactionRewardsService_1.processReactionReward)(fullReaction, fullUser);
        // Track reaction activity for milestones
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(client, fullUser.id, fullReaction.message.guild?.id || '', 'reaction', {
                channelId: fullReaction.message.channel.id,
                emoji: fullReaction.emoji,
                timestamp: new Date()
            });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] User ${fullUser.tag} achieved ${milestoneResults.length} milestone(s) from reaction activity`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing reaction milestones:', error);
        }
    }
    catch (error) {
        console.error('[Reaction Handler] Error in messageReactionAdd handler:', error);
    }
});
// Guild member add event handler for automated messages and milestone tracking
client.on('guildMemberAdd', async (member) => {
    try {
        // Process join messages
        const joinResult = await (0, automessageService_1.processJoinMessage)(member);
        if (joinResult.sent) {
            console.log(`[AutoMessage] Sent ${joinResult.templatesProcessed} join message(s) to ${member.displayName} in ${member.guild.name}`);
        }
        if (joinResult.errors.length > 0) {
            console.error(`[AutoMessage] Errors processing join messages for ${member.displayName}:`, joinResult.errors);
        }
        // Track login activity for milestones (new member joining counts as login)
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(client, member.user.id, member.guild.id, 'login', { timestamp: new Date() });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] New member ${member.displayName} achieved ${milestoneResults.length} milestone(s) on join`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing join milestones:', error);
        }
        // Note: Starter balance is processed in guildMemberUpdate when roles are added
    }
    catch (error) {
        console.error('[Member Add] Error in guildMemberAdd handler:', error);
    }
});
// Guild member remove event handler for user data cleanup
client.on('guildMemberRemove', async (member) => {
    try {
        // Handle partial members
        if (member.partial) {
            try {
                await member.fetch();
            }
            catch (error) {
                console.error('[User Cleanup] Failed to fetch member data:', error);
                // Continue with cleanup using available data
            }
        }
        const userId = member.user?.id;
        const guildName = member.guild?.name || 'Unknown Guild';
        const displayName = member.displayName || member.user?.username || 'Unknown User';
        if (!userId) {
            console.error('[User Cleanup] No user ID available for member who left');
            return;
        }
        // Check if we have user data before attempting cleanup
        const userData = await userCleanupService_1.UserCleanupService.checkUserData(userId);
        const hasData = userData.hasUserRecord || userData.transactionCount > 0 || userData.reactionRewardCount > 0;
        if (!hasData) {
            console.log(`[User Cleanup] No data found for ${displayName} (${userId}) who left ${guildName}, skipping cleanup`);
            return;
        }
        console.log(`[User Cleanup] User ${displayName} left ${guildName} - found data: ${userData.hasUserRecord ? 'balance' : ''} ${userData.transactionCount > 0 ? `${userData.transactionCount} transactions` : ''} ${userData.reactionRewardCount > 0 ? `${userData.reactionRewardCount} reaction rewards` : ''}`.trim());
        // Perform cleanup - we need to cast to GuildMember for the cleanup service
        const cleanupResult = await userCleanupService_1.UserCleanupService.cleanupUserData(member);
        if (cleanupResult.success) {
            const removedItems = [];
            if (cleanupResult.userDataRemoved)
                removedItems.push('user balance');
            if (cleanupResult.transactionsRemoved > 0)
                removedItems.push(`${cleanupResult.transactionsRemoved} transactions`);
            if (cleanupResult.reactionRewardsRemoved > 0)
                removedItems.push(`${cleanupResult.reactionRewardsRemoved} reaction rewards`);
            console.log(`[User Cleanup] Successfully cleaned up data for ${displayName}: ${removedItems.join(', ')} (${cleanupResult.timeTaken}ms)`);
        }
        else {
            console.error(`[User Cleanup] Failed to clean up data for ${displayName}:`, cleanupResult.errors);
        }
    }
    catch (error) {
        console.error(`[User Cleanup] Error in guildMemberRemove handler:`, error);
    }
});
// Guild member update event handler for starter balance and role change messages
client.on('guildMemberUpdate', async (oldMember, newMember) => {
    try {
        // Handle partial members
        if (oldMember.partial) {
            try {
                await oldMember.fetch();
            }
            catch (error) {
                console.error('[Member Update] Failed to fetch old member:', error);
                return;
            }
        }
        // Check for role changes
        const addedRoles = newMember.roles.cache.filter(role => !oldMember.roles.cache.has(role.id));
        const removedRoles = oldMember.roles.cache.filter(role => !newMember.roles.cache.has(role.id));
        // Process added roles
        if (addedRoles.size > 0) {
            for (const [roleId, role] of addedRoles) {
                try {
                    // Process starter balance
                    const granted = await (0, starterBalanceService_1.processStarterBalance)(newMember, role);
                    if (granted) {
                        console.log(`[Starter Balance] Granted starter balance to ${newMember.displayName} for role ${role.name}`);
                    }
                    // Process role add messages
                    const roleAddResult = await (0, automessageService_1.processRoleChangeMessage)(newMember, role, 'role_add');
                    if (roleAddResult.sent) {
                        console.log(`[AutoMessage] Sent ${roleAddResult.templatesProcessed} role add message(s) to ${newMember.displayName} for role ${role.name}`);
                    }
                    if (roleAddResult.errors.length > 0) {
                        console.error(`[AutoMessage] Errors processing role add messages for ${newMember.displayName}:`, roleAddResult.errors);
                    }
                }
                catch (error) {
                    console.error(`[Member Update] Failed to process role add for ${newMember.displayName} and role ${role.name}:`, error);
                }
            }
        }
        // Process removed roles
        if (removedRoles.size > 0) {
            for (const [roleId, role] of removedRoles) {
                try {
                    // Process role remove messages
                    const roleRemoveResult = await (0, automessageService_1.processRoleChangeMessage)(newMember, role, 'role_remove');
                    if (roleRemoveResult.sent) {
                        console.log(`[AutoMessage] Sent ${roleRemoveResult.templatesProcessed} role remove message(s) to ${newMember.displayName} for role ${role.name}`);
                    }
                    if (roleRemoveResult.errors.length > 0) {
                        console.error(`[AutoMessage] Errors processing role remove messages for ${newMember.displayName}:`, roleRemoveResult.errors);
                    }
                }
                catch (error) {
                    console.error(`[Member Update] Failed to process role remove for ${newMember.displayName} and role ${role.name}:`, error);
                }
            }
        }
    }
    catch (error) {
        console.error('[Member Update] Error in guildMemberUpdate handler:', error);
    }
});
// Voice state update handler for milestone tracking
client.on('voiceStateUpdate', async (oldState, newState) => {
    try {
        // Skip bot users
        if (newState.member?.user.bot)
            return;
        const userId = newState.member?.user.id;
        const guildId = newState.guild.id;
        if (!userId)
            return;
        // Track voice activity for milestones
        // We'll track when users join voice channels
        if (!oldState.channel && newState.channel) {
            // User joined a voice channel
            try {
                const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(client, userId, guildId, 'voice', {
                    channelId: newState.channel.id,
                    minutes: 1, // Initial join counts as 1 minute
                    timestamp: new Date()
                });
                if (milestoneResults.length > 0) {
                    console.log(`[Milestone] User ${newState.member?.displayName} achieved ${milestoneResults.length} milestone(s) from voice activity`);
                }
            }
            catch (error) {
                console.error('[Milestone] Error processing voice milestones:', error);
            }
        }
    }
    catch (error) {
        console.error('[Voice State] Error in voiceStateUpdate handler:', error);
    }
});
client.login(process.env.BOT_TOKEN);
